package binlogtosql_test

import (
	"fmt"

	binlogtosql "mdc-agent/binlogToSql"
	"testing"

	"github.com/smartystreets/goconvey/convey"
)

var (
	InsertSQL     string = "INSERT INTO `binlogtosqltest`.`insertTest` (`ID`,`str`,`flo`,`dou`,`deci`,`dattime`,`txt`,`blo`,`small`,`mediu`,`bgint`,`bt`,`tim`,`yea`,`dat`,`ch`) VALUES (1,'123',2.5,2.5,1,'0000-00-00 00:00:00',X'3132333435',X'3132333435',1,2,3,1,'15:00:00',2000,'2000-01-02','q')"
	DeleteSQL     string = "DELETE FROM `binlogtosqltest`.`deleteTest` WHERE `ID`=1"
	UpdateSQL     string = "UPDATE `binlogtosqltest`.`updateTest` SET `str`='hello world' WHERE `ID`=1"
	DeleteSQLNoPK string = "DELETE FROM `binlogtosqltest`.`noprimarykey` WHERE (`val1`=1 AND `val2`=2)"
	UpdateSQLNoPK string = "UPDATE `binlogtosqltest`.`noprimarykey` SET `val1`=1 WHERE (`val1`=1 AND `val2`=2)"
)

func TestParseFile(t *testing.T) {
	File := []string{"/Users/<USER>/Desktop/mysql-bin.000128"}
	convey.Convey("Parse Test", t, func() {
		SQLS, err := binlogtosql.ParseFile(binlogtosql.ParseFileParams{
			Host:      "*************",
			Port:      "8510",
			User:      "net_monitor",
			Passwd:    "net_monitor741",
			FileList:  File,
			StartTime: "2021-01-01 16:59:20",
			EndTime:   "2021-10-24 00:00:00"})
		convey.So(err, convey.ShouldBeNil)
		convey.So(SQLS, convey.ShouldNotBeNil)
		fmt.Println(SQLS)
	})
}

func TestInsert(t *testing.T) {
	File := []string{"../binlogs/insertTest"}
	convey.Convey("Parse Insert Test", t, func() {
		SQLS, err := binlogtosql.ParseFile(binlogtosql.ParseFileParams{Host: "*************",
			Port:      "8889",
			User:      "test",
			Passwd:    "1234",
			FileList:  File,
			StartTime: "2021-01-01 16:59:20",
			EndTime:   "2021-08-31 00:00:00"})
		convey.So(err, convey.ShouldBeNil)
		convey.So(len(SQLS), convey.ShouldEqual, 1)
		convey.So(SQLS[0],
			convey.ShouldEqual,
			InsertSQL)
	})
}

func TestDelete(t *testing.T) {
	File := []string{"../binlogs/deleteTest"}
	convey.Convey("Parse Delete Test", t, func() {
		SQLS, err := binlogtosql.ParseFile(binlogtosql.ParseFileParams{Host: "*************",
			Port:      "8889",
			User:      "test",
			Passwd:    "1234",
			FileList:  File,
			StartTime: "2021-01-01 16:59:20",
			EndTime:   "2021-08-31 00:00:00"})
		convey.So(err, convey.ShouldBeNil)
		convey.So(len(SQLS), convey.ShouldEqual, 1)
		convey.So(SQLS[0],
			convey.ShouldEqual,
			DeleteSQL)
	})
}
func TestUpdate(t *testing.T) {
	File := []string{"../binlogs/updateTest"}
	convey.Convey("Parse Update Test", t, func() {
		SQLS, err := binlogtosql.ParseFile(binlogtosql.ParseFileParams{Host: "*************",
			Port:      "8889",
			User:      "test",
			Passwd:    "1234",
			FileList:  File,
			StartTime: "2021-01-01 16:59:20",
			EndTime:   "2021-08-31 00:00:00"})
		convey.So(err, convey.ShouldBeNil)
		convey.So(len(SQLS), convey.ShouldEqual, 1)
		convey.So(SQLS[0],
			convey.ShouldEqual,
			UpdateSQL)
	})
}

func TestDropTable(t *testing.T) {
	File := []string{"../binlogs/dropTableTest"}
	convey.Convey("DropTable错误测试", t, func() {
		_, err := binlogtosql.ParseFile(binlogtosql.ParseFileParams{Host: "*************",
			Port:      "8889",
			User:      "test",
			Passwd:    "1234",
			FileList:  File,
			StartTime: "2021-01-01 16:59:20",
			EndTime:   "2021-08-31 00:00:00"})
		convey.So(err.Error(), convey.ShouldContainSubstring, "maybe dropped")
	})
}

func TestNopkTable(t *testing.T) {
	File := []string{"../binlogs/no_primary_key"}
	convey.Convey("无主键表错误测试", t, func() {
		SQLS, err := binlogtosql.ParseFile(binlogtosql.ParseFileParams{Host: "*************",
			Port:      "8889",
			User:      "test",
			Passwd:    "1234",
			FileList:  File,
			StartTime: "2021-01-01 16:59:20",
			EndTime:   "2021-08-31 00:00:00"})
		convey.So(err, convey.ShouldBeNil)
		convey.So(len(SQLS), convey.ShouldNotEqual, 0)
		convey.So(SQLS[2], convey.ShouldEqual, DeleteSQLNoPK)
		convey.So(SQLS[3], convey.ShouldEqual, UpdateSQLNoPK)
	})
}

func TestChangeTable(t *testing.T) {
	File := []string{"../binlogs/DDLTest1", "../binlogs/DDLTest2"}
	convey.Convey("删除列错误测试", t, func() {
		_, err := binlogtosql.ParseFile(binlogtosql.ParseFileParams{Host: "*************",
			Port:      "8889",
			User:      "test",
			Passwd:    "1234",
			FileList:  File,
			StartTime: "2021-01-01 16:59:20",
			EndTime:   "2021-08-31 00:00:00"})
		convey.So(err.Error(), convey.ShouldContainSubstring, "DDL happens")
	})
	convey.Convey("修改字段类型错误测试", t, func() {
		_, err := binlogtosql.ParseFile(binlogtosql.ParseFileParams{Host: "*************",
			Port:      "8889",
			User:      "test",
			Passwd:    "1234",
			FileList:  File,
			StartTime: "2021-01-01 16:59:20",
			EndTime:   "2021-08-31 00:00:00"})
		convey.So(err.Error(), convey.ShouldContainSubstring, "DDL happens")
	})
}
