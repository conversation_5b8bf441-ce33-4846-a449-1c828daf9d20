package xtrabackup

import (
	"context"
	"sync"
	"time"
)

// BackupType 备份类型
type BackupType int

const (
	BackupTypeFull        BackupType = iota // 全量备份
	BackupTypeIncremental                   // 增量备份
)

// BackupStatus 备份状态
type BackupStatus int

const (
	StatusIdle       BackupStatus = iota // 空闲
	StatusRunning                        // 运行中
	StatusPaused                         // 已暂停
	StatusCompleted                      // 已完成
	StatusFailed                         // 失败
	StatusStopped                        // 已停止
)

// Config XtraBackup 配置
type Config struct {
	// 基础配置
	BinPath         string `json:"bin_path"`          // innobackupex 二进制路径
	XbstreamPath    string `json:"xbstream_path"`     // xbstream 二进制路径
	
	// MySQL 连接配置
	MySQLBaseDir    string `json:"mysql_base_dir"`    // MySQL 基础目录
	MySQLUser       string `json:"mysql_user"`        // MySQL 用户名
	MySQLPassword   string `json:"mysql_password"`    // MySQL 密码
	MySQLSocket     string `json:"mysql_socket"`      // MySQL socket 路径
	
	// 备份配置
	ThrottleRate    int64  `json:"throttle_rate"`     // 限速 MB/s
	CompressLevel   int    `json:"compress_level"`    // 压缩级别
	CompressThreads int    `json:"compress_threads"`  // 压缩线程数
	UseMemory       string `json:"use_memory"`        // apply-log 使用内存
	
	// 高级配置
	KillLongQueryType    string `json:"kill_long_query_type"`    // 杀死长查询类型
	KillLongQueryTimeout string `json:"kill_long_query_timeout"` // 杀死长查询超时
	ExtraOptions         string `json:"extra_options"`           // 额外选项
	
	// 输出配置
	StreamFormat    string `json:"stream_format"`     // 流格式 (xbstream)
	NoTimestamp     bool   `json:"no_timestamp"`      // 不使用时间戳目录
	SlaveInfo       bool   `json:"slave_info"`        // 记录从库信息
}

// BackupOptions 备份选项
type BackupOptions struct {
	Type            BackupType `json:"type"`             // 备份类型
	LSNPosition     string     `json:"lsn_position"`     // LSN 位置（增量备份用）
	TargetDir       string     `json:"target_dir"`       // 目标目录
	ExtraLSNDir     string     `json:"extra_lsn_dir"`    // 额外 LSN 目录
	RemoteHost      string     `json:"remote_host"`      // 远程主机
	RemotePath      string     `json:"remote_path"`      // 远程路径
	SSHKeyPath      string     `json:"ssh_key_path"`     // SSH 密钥路径
	BackupOptions   string     `json:"backup_options"`   // 额外备份选项
}

// RestoreOptions 恢复选项
type RestoreOptions struct {
	BackupPath      string   `json:"backup_path"`       // 备份路径
	TargetDir       string   `json:"target_dir"`        // 目标目录
	IncrementalDirs []string `json:"incremental_dirs"`  // 增量备份目录列表
	UseMemory       string   `json:"use_memory"`        // 使用内存大小
	ParallelThreads int      `json:"parallel_threads"`  // 并行线程数
}

// BackupResult 备份结果
type BackupResult struct {
	Success     bool      `json:"success"`      // 是否成功
	StartTime   time.Time `json:"start_time"`   // 开始时间
	EndTime     time.Time `json:"end_time"`     // 结束时间
	Duration    time.Duration `json:"duration"` // 持续时间
	DataSize    string    `json:"data_size"`    // 数据大小
	LSNFrom     string    `json:"lsn_from"`     // 起始 LSN
	LSNTo       string    `json:"lsn_to"`       // 结束 LSN
	BackupPath  string    `json:"backup_path"`  // 备份路径
	ErrorMsg    string    `json:"error_msg"`    // 错误信息
}

// RestoreResult 恢复结果
type RestoreResult struct {
	Success    bool          `json:"success"`     // 是否成功
	StartTime  time.Time     `json:"start_time"`  // 开始时间
	EndTime    time.Time     `json:"end_time"`    // 结束时间
	Duration   time.Duration `json:"duration"`    // 持续时间
	DataPath   string        `json:"data_path"`   // 恢复数据路径
	ErrorMsg   string        `json:"error_msg"`   // 错误信息
}

// ProgressInfo 进度信息
type ProgressInfo struct {
	Status       BackupStatus  `json:"status"`        // 当前状态
	Progress     float64       `json:"progress"`      // 进度百分比 (0-100)
	CurrentFile  string        `json:"current_file"`  // 当前处理文件
	ProcessedMB  int64         `json:"processed_mb"`  // 已处理 MB
	TotalMB      int64         `json:"total_mb"`      // 总 MB
	Speed        int64         `json:"speed"`         // 当前速度 MB/s
	ETA          time.Duration `json:"eta"`           // 预计剩余时间
	Message      string        `json:"message"`       // 状态消息
}

// Manager XtraBackup 管理器接口
type Manager interface {
	// 备份相关
	FullBackup(ctx context.Context, opts *BackupOptions) (*BackupResult, error)
	IncrementalBackup(ctx context.Context, opts *BackupOptions) (*BackupResult, error)
	
	// 恢复相关
	PrepareBackup(ctx context.Context, opts *RestoreOptions) (*RestoreResult, error)
	RestoreBackup(ctx context.Context, opts *RestoreOptions) (*RestoreResult, error)
	
	// 控制相关
	Pause() error
	Resume() error
	Stop() error
	SetThrottleRate(rate int64) error
	
	// 状态查询
	GetStatus() BackupStatus
	GetProgress() *ProgressInfo
	IsRunning() bool
	
	// 进程管理
	CheckProcess() (bool, error)
	KillProcess() error
}

// RateLimiter 限速控制器
type RateLimiter struct {
	rate     int64         // 当前限速 MB/s
	mutex    sync.RWMutex  // 读写锁
	enabled  bool          // 是否启用限速
}

// ProgressMonitor 进度监控器
type ProgressMonitor struct {
	info     *ProgressInfo
	mutex    sync.RWMutex
	callback func(*ProgressInfo) // 进度回调函数
}

// ProcessManager 进程管理器
type ProcessManager struct {
	pid       int           // 进程 ID
	cmd       string        // 执行命令
	startTime time.Time     // 启动时间
	mutex     sync.RWMutex  // 读写锁
}

// XtraInfo XtraBackup 信息解析结果
type XtraInfo struct {
	DataSize string `json:"data_size"` // 数据大小
	LSNFrom  string `json:"lsn_from"`  // 起始 LSN
	LSNTo    string `json:"lsn_to"`    // 结束 LSN
	EndTime  string `json:"end_time"`  // 结束时间
}

// ReplicationConfig 并行复制配置
type ReplicationConfig struct {
	Workers              int  `json:"workers"`                // 并行工作线程数
	PreserveCommitOrder  bool `json:"preserve_commit_order"`  // 保持提交顺序
	AutoManage           bool `json:"auto_manage"`            // 自动管理并行复制
}
