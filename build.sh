#!/bin/bash
source ./go-env-build.sh

mkdir -p output output/status/mdc-agent output/logs && touch output/status/mdc-agent/status
make
cp supervise ./output/bin/supervise.mdc-agent
cp -r conf bin mdc-agent-control ./output

#下载备份恢复工具
wget -O mdc_tool_bin.tgz https://sqlonline.bcebos.hb-fsg.x3.dxmyun.com/mdc_tool_bin.tgz?authorization=bce-auth-v1/939f36b2d4404fb390189c5490363c2e/2023-06-12T03%3A36%3A28Z/-1/host/96c913ff7f20957fce5f7546063b4b14066fa4fb717d06782d6573b77faef074 && mkdir mdc_tool_bin && tar -zxf mdc_tool_bin.tgz -C mdc_tool_bin && rm -rf mdc_tool_bin.tgz
mv mdc_tool_bin/bin/* ./output/bin && mv  mdc_tool_bin/bin/.id_rsa_tmp_dbbk ./output/bin && rm -rf mdc_tool_bin/bin
