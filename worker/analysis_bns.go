package worker

import (
	"errors"
	"fmt"
	"net"
	"sort"
	"strings"
	"sync"
	"time"

	"mdc-agent/common"
)

type bnsCollectorTimer struct {
	Tick                 *time.Ticker
	BnsCollectorStopChan chan bool
}

var bnsCollectorTimerController = new(bnsCollectorTimer)

func StartAnalysisBns(mWaitGroup *sync.WaitGroup) {
	bnsCollectorTimerController.Tick = time.NewTicker(time.Second * time.Duration(common.Config.BnsReloadCycle))
	for {
		select {
		case <-bnsCollectorTimerController.Tick.C:
			//执行Bns定时更新
			if err := UpdateIpList(); err != nil {
				common.Log.Warning("mdc-agent : AnalysisBns and update ipList get some errors. err=[%v]", err)
			}
		case stop := <-bnsCollectorTimerController.BnsCollectorStopChan:
			if stop {
				//优雅退出信号处理
				fmt.Println("receive exit signal, bnsStopChan has been closed.")
				mWaitGroup.Done()
				return
			}
		}
	}
}
func StopAnalysisBns() {
	bnsCollectorTimerController.Tick.Stop()
	bnsCollectorTimerController.BnsCollectorStopChan <- true
	close(bnsCollectorTimerController.BnsCollectorStopChan)
}

func geterrMsg(statusCode int, err error) error {
	switch statusCode {
	case 124:
		return errors.New("ExecShell timeout")
	case 254, 255:
		return errors.New("bns does not exist")
	default:
		return err
	}
	return err
}

// 通过shell命令将bns解析为ip列表
func GetInstanceListByService(bnsStr string) ([]string, error) {
	//执行get_instance_by_cloudapp 命令
	cmdStr := fmt.Sprintf("timeout %v get_instance_by_cloudapp -i %s", common.Config.AnalysisBnsTimeout, bnsStr)
	IpResult, statusCode, err := common.SyncExecShell(cmdStr)
	if err != nil {
		common.Log.Warn("get_instance_by_cloudapp failed:bns=[%v],err:=[%v]", bnsStr, geterrMsg(statusCode, err))
		return nil, geterrMsg(statusCode, err)
	} else if "" == IpResult {
		//执行get_instance_by_cloudapp获得的结果为空 进行重试
		for i := 0; i < 3; i++ {
			time.Sleep(1 * time.Second)
			IpResult, statusCode, err = common.SyncExecShell(cmdStr)
			if err != nil {
				return nil, geterrMsg(statusCode, err)
			}
			if "" != IpResult {
				break
			}
		}
	}
	if "" == IpResult {
		return nil, nil
	}
	//对执行get_instance_by_cloudapp获得的结果进行切割拿到IP列
	var instanceList []string
	instanceInfo := strings.Split(IpResult, "\n")
	for _, temp := range instanceInfo {
		instance := strings.FieldsFunc(strings.Trim(temp, " \r\n\t"), func(r rune) bool {
			if r == rune(' ') || r == rune('\t') {
				return true
			}
			return false
		})
		if 2 == len(instance) {
			insInfo := strings.Trim(instance[1], " \r\n\t")
			if "" != insInfo {
				instanceList = append(instanceList, insInfo)
			}
		}
	}
	return instanceList, nil
}

func UpdateIpList() error {
	// 更新mdc server的ipList
	mdcServerListIpList, err := getNewIpList(common.Config.MdcRpcServer)
	if nil != err {
		common.Log.Warn("update ip list failed. err=[%v]", err)
		return err
	}

	if !StringSliceEqual(common.MdcServerIpList.IpList, mdcServerListIpList) {
		common.MdcServerIpList.RwLock.Lock()
		common.Log.Notice("mdc-server ip list has changed. oldList=[%v] newList=[%v]", common.MdcServerIpList.IpList, mdcServerListIpList)
		common.MdcServerIpList.IpList = mdcServerListIpList
		common.MdcServerIpList.RwLock.Unlock()
	}
	return nil
}

//获取mdc-server Ip List
func getNewIpList(rpcdsn *common.Rpcdsn) ([]string, error) {
	ipList, err := GetInstanceListByService(rpcdsn.Addr)
	if nil != err {
		common.Log.Warn("get new ip list failed. err=[%v]", err)
		return nil, err
	}

	ipListNew := make([]string, 0, len(ipList))
	//对ipList中ip进行ip校验
	for idx, ip := range ipList {
		// 校验端口号是否启动，如果没启动则跳过本ip
		checkPortStr := fmt.Sprintf("%v:%v", ip, rpcdsn.Port)
		_, err := net.DialTimeout("tcp4", checkPortStr, time.Second)
		if nil != err {
			common.Log.Warn("get new ip list failed. addr=[%v] err=[%v]", checkPortStr, err)
			continue
		}

		// ParseIP 这个方法 可以用来检查 ip 地址是否正确，如果不正确，该方法返回 nil
		address := net.ParseIP(ip)
		if address != nil {
			ipListNew = append(ipListNew, ipList[idx])
		}
	}

	if 0 == len(ipListNew) {
		errstring := fmt.Sprintf("The ipList analyzed according to BNS is empty, global csIpList will not be updated")
		common.Log.Warn(errstring)
		return nil, errors.New(errstring)
	}

	return ipListNew, err
}

func StringSliceEqual(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	if (a == nil) != (b == nil) {
		return false
	}
	sort.Strings(a)
	sort.Strings(b)
	for i, v := range a {
		if v != b[i] {
			return false
		}
	}
	return true
}
