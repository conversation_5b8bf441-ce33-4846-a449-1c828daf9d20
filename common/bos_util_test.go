package common

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func init() {
	Config.DbaBosAccessKeyID = "77cbfd63fe57424db581eeac513ed86b"
	Config.DbaBosAccessKeySecret = "6645ebbd54f94876ad2de7efeda9d957"
	Config.DbaBosBucket = "dxm-dbbk-hb-test"
	Config.DbaBosEndPoint = "hb-fsg.bcebos.com"
}

func TestCreatBosClient(t *testing.T) {
	Convey("test CreatDbaBosClient", t, func() {
		bosClient, err := CreatBosClient(Config.DbaBosAccessKeyID, Config.DbaBosAccessKeySecret, Config.DbaBosEndPoint)
		So(err, ShouldBeNil)
		So(bosClient, ShouldNotBeNil)
	})
}

func TestUploadObjToBos(t *testing.T) {
	Convey("test UploadObjToBos", t, func() {
		sqls := []string{"select * from test00.testa"}
		bosClient, err := UploadObjToBos(sqls, 20210924)
		So(err, ShouldBeNil)
		So(bosClient, ShouldNotBeNil)
	})
}

func TestBlockUploadToBos(t *testing.T) {
	Convey("test BlockUploadToBos", t, func() {
		filePath := "/Users/<USER>/binlogTestDir"
		bosPath := "20210924_Base/base"

		//分块上传数据
		isSuccess, err := BlockUploadToBos( filePath, bosPath)
		So(err, ShouldBeNil)
		So(isSuccess, ShouldBeTrue)
	})
}

func TestDownLoadSqlFileFromBos(t *testing.T) {
	Convey("test DownLoadDataFromBos", t, func() {
		baseDataDir := "20210924_Base/base"
		targetDir := "/Users/<USER>/base.sql"
		err := DownLoadDataFromBos(Config.DbaBosAccessKeyID, Config.DbaBosAccessKeySecret, Config.DbaBosEndPoint, Config.DbaBosBucket, baseDataDir, targetDir)
		So(err, ShouldBeNil)
	})
}

func TestDeleteBosData(t *testing.T) {
	Convey("test DownLoadDataFromBos", t, func() {
		dataDir := []string{"20210924_Base/base"}
		err := DeleteBosData(dataDir)
		So(err, ShouldBeNil)
	})
}
