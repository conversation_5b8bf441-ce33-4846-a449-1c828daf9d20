package binlogtosql_test

import (
	binlogtosql "mdc-agent/binlogToSql"
	"sync"
	"testing"

	"github.com/go-mysql-org/go-mysql/replication"
	"github.com/smartystreets/goconvey/convey"
)

var (
	cfg        *binlogtosql.Configs = new(binlogtosql.Configs)
	Host       string               = "*************"
	Port       string               = "8889"
	InsertFile string               = "../binlogs/insertTest"
	UpdateFile string               = "../binlogs/updateTest"
	DeleteFile string               = "../binlogs/deleteTest"
	User       string               = "test"
	Pwd        string               = "1234"
	StartTime  string               = "2000-01-02 15:00:00"
	EndTime    string               = "2022-01-01 00:00:00"
)

func init() {
	cfg.FileList = []string{InsertFile}
	cfg.Host = Host
	cfg.Port = Port
	cfg.User = User
	cfg.Passwd = Pwd
	cfg.StartTime = StartTime
	cfg.EndTime = EndTime
	cfg.SetTime()
	cfg.OpenChansAndDb()
}
func TestParseEvent(t *testing.T) {
	var wg sync.WaitGroup
	events := []*replication.BinlogEvent{}
	wg.Add(1)
	convey.Convey("Insert过滤测试", t, func() {
		go binlogtosql.ParseEventsFromFile(cfg, &wg)
		for {
			select {
			case event := <-cfg.EventChan:
				events = append(events, event)
				break
			case <-cfg.StatusChan:
				goto TEST
			}
		}
	TEST:
		for _, e := range events {
			convey.So(e.Header.EventType, convey.ShouldBeIn, replication.WRITE_ROWS_EVENTv0, replication.WRITE_ROWS_EVENTv1, replication.WRITE_ROWS_EVENTv2)
		}
	})

	wg.Add(1)
	cfg.FileList = []string{DeleteFile}
	events = []*replication.BinlogEvent{}
	convey.Convey("Delete过滤测试", t, func() {
		go binlogtosql.ParseEventsFromFile(cfg, &wg)
		for {
			select {
			case event := <-cfg.EventChan:
				events = append(events, event)
				break
			case <-cfg.StatusChan:
				goto TEST
			}
		}
	TEST:
		for _, e := range events {
			convey.So(e.Header.EventType, convey.ShouldBeIn, replication.DELETE_ROWS_EVENTv0, replication.DELETE_ROWS_EVENTv1, replication.DELETE_ROWS_EVENTv2)
		}
	})

	wg.Add(1)
	cfg.FileList = []string{UpdateFile}
	events = []*replication.BinlogEvent{}
	convey.Convey("Update过滤测试", t, func() {
		go binlogtosql.ParseEventsFromFile(cfg, &wg)
		for {
			select {
			case event := <-cfg.EventChan:
				events = append(events, event)
				break
			case <-cfg.StatusChan:
				goto TEST
			}
		}
	TEST:
		for _, e := range events {
			convey.So(e.Header.EventType, convey.ShouldBeIn, replication.UPDATE_ROWS_EVENTv0, replication.UPDATE_ROWS_EVENTv1, replication.UPDATE_ROWS_EVENTv2)
		}
	})

	convey.Convey("时间过滤测试", t, func() {
		cfg.EndTime = "2021-07-01 00:00:00"
		cfg.SetTime()
		wg.Add(1)
		events = []*replication.BinlogEvent{}
		go binlogtosql.ParseEventsFromFile(cfg, &wg)
		for {
			select {
			case event := <-cfg.EventChan:
				events = append(events, event)
				break
			case <-cfg.StatusChan:
				goto TEST
			}
		}
	TEST:
		convey.So(len(events), convey.ShouldEqual, 0)
	})

}

func TestTime(t *testing.T) {
	convey.Convey("错误时间测试", t, func() {
		var err error
		cfg.StartTime = "2021-07-02 00:00:00"
		cfg.EndTime = "2021-07-01 00:00:00"
		err = cfg.SetTime()
		convey.So(err.Error(), convey.ShouldContainSubstring, "starttime must earlier than endtime")
		cfg.StartTime = "1232114"
		err = cfg.SetTime()
		convey.So(err.Error(), convey.ShouldContainSubstring, "invalid starttime")
		cfg.StartTime = "2021-07-02 00:00:00"
		cfg.EndTime = "123231"
		err = cfg.SetTime()
		convey.So(err.Error(), convey.ShouldContainSubstring, "invalid endtime")
	})
}
