package common

import (
	"fmt"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestSyncExecShell(t *testing.T) {
	<PERSON>vey("test SyncExecShell", t, func() {
		_, _, err := SyncExecShell("")
		So(err, ShouldBeNil)
	})

	<PERSON>vey("test SyncExecShell", t, func() {
		_, _, err := SyncExecShell("ls -l")
		So(err, ShouldBeNil)
	})
}

func TestDiskUsage(t *testing.T) {
	<PERSON>vey("test DiskUsage", t, func() {
		_, err := DiskUsage("")
		So(err, ShouldNotBeNil)
	})

	Convey("test DiskUsage", t, func() {
		_, err := DiskUsage("/Users/<USER>")
		So(err, ShouldBeNil)
	})
}

func TestRandString(t *testing.T) {
	<PERSON><PERSON>("test RandString", t, func() {
		passwordStr := RandString(15)
		So(passwordStr, ShouldNotBeNil)
		fmt.Println(passwordStr)
	})
}

func TestPathExists(t *testing.T) {
	<PERSON><PERSON>("test PathExists", t, func() {
		_, err := PathExists("/Users/<USER>")
		So(err, ShouldBeNil)
	})
}

func TestCleanMachineData(t *testing.T) {
	Convey("test CleanMachineData", t, func() {
		err := CleanMachineData([]string{"/Users/<USER>"}, 1)
		So(err, ShouldBeNil)
	})
}

