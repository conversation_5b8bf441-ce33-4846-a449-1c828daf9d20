package common

import (
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net"
	"os"
	"os/exec"
	"runtime"
	"strconv"
	"syscall"
	"text/template"
	"time"

	pb_server "dt-common/protobuf/mdc-server"
)

// 获取磁盘空间
type DiskStatus struct {
	All  uint64  // 磁盘总空间 （Byte）
	Used uint64  // 磁盘已使用空间 （Byte）
	Free uint64  // 磁盘剩余空间 （Byte）
	Pct  float32 // 磁盘剩余百分比
}

const (
	GB uint64 = 1024 * 1024 * 1024
)

func SyncExecShell(cmdStr string) (outStr string, status int, err error) {
	defer func() {
		r := recover()
		if errs, ok := r.(error); ok {
			const size = 4096
			buf := make([]byte, size)
			buf = buf[:runtime.Stack(buf, false)]
			err = errors.New(fmt.Sprintf("err=%v, stack=%v", errs, string(buf)))
		}
	}()
	command := exec.Command("/bin/bash", "-c", cmdStr)
	out, err := command.Output()
	statusCode, ok := command.ProcessState.Sys().(syscall.WaitStatus)
	if ok {
		status = statusCode.ExitStatus()
	} else if err != nil {
		// 当无法获取到命令行返回值时，如果err不为空将返回值置为-1
		status = -1
	}
	Log.Notice("Execute shell command. cmdStr=[%v] exitCode=[%v] stdout=[%v] stderr[%v]",
		cmdStr, status, string(out), err)
	if err != nil {
		//超时退出码为124
		if 124 == status {
			return string(out), status, errors.New(fmt.Sprintf("ExecShell timeout,err=%s", err.Error()))
		}
		if ee, ok := err.(*exec.ExitError); ok {
			return string(out), status, errors.New(fmt.Sprintf("%s,%s", err.Error(), string(ee.Stderr)))
		}
		return string(out), status, err
	}
	return string(out), status, nil
}

func SyncExecShellNoResult(cmdStr string) (err error) {
	defer func() {
		r := recover()
		if errs, ok := r.(error); ok {
			const size = 4096
			buf := make([]byte, size)
			buf = buf[:runtime.Stack(buf, false)]
			err = errors.New(fmt.Sprintf("err=%v, stack=%v", errs, string(buf)))
		}
	}()
	command := exec.Command("/bin/bash", "-c", cmdStr)
	err = command.Start()
	Log.Notice("Execute shell command. cmdStr=[%v] stderr[%v]",
		cmdStr, err)
	if err != nil {
		return err
	}
	return nil
}

// 获取指定路径对应磁盘的空间
func DiskUsage(path string) (disk DiskStatus, err error) {
	if path == "" {
		return disk, errors.New("path is null")
	}
	fs := syscall.Statfs_t{}
	err = syscall.Statfs(path, &fs)
	if err != nil {
		Log.Warn("Get disk Usage failed. path=[%v] err=[%v]", path, err)
		return disk, err
	}
	disk.All = fs.Blocks * uint64(fs.Bsize) / GB
	disk.Free = fs.Bfree * uint64(fs.Bsize) / GB
	disk.Used = disk.All - disk.Free
	pctStr := fmt.Sprintf("%.2f", float32(disk.Used)/float32(disk.All)*100)
	pctFloat32, err := strconv.ParseFloat(pctStr, 32)
	if err != nil {
		Log.Warn("Get disk Usage failed. path=[%v] err=[%v]", path, err)
		return disk, err
	}
	disk.Pct = float32(pctFloat32)
	return disk, nil
}

//清理大磁盘备份数据
func CleanMachineData(dataDir []string, dataType pb_server.DataType) (err error) {
	if len(dataDir) == 0 || dataDir == nil {
		return
	}
	for _, val := range dataDir {
		if val == "" {
			continue
		}
		//判断清理数据的类型 如果是恢复的数据
		if dataType == pb_server.DataType_RS_REMOTE_DATA {
			getInstancePid := fmt.Sprintf("ps -ef | grep %v|grep -v grep |awk '{print $2}'", val)
			pid, _, err := SyncExecShell(getInstancePid)
			if err != nil {
				Log.Error("SyncExecShell getInstancePid failed,err=[%v],shell=[%v],shell=[%v]", err, getInstancePid)
				return err
			}
			if pid != "" {
				//进程存在的话 kill进程
				stopPid := fmt.Sprintf("kill -9 %s", pid)
				_, _, err = SyncExecShell(stopPid)
				if err != nil {
					Log.Error("SyncExecShell stopPid failed,err=[%v],shell=[%v]", err, stopPid)
					return err
				}
			}
		}
		exist, err := PathExists(val)
		if err != nil {
			fmt.Printf("PathExists=[%s],err=[%v]", val, err)
		}
		if exist {
			err = os.RemoveAll(val)
			if err != nil {
				Log.Error("Exec CleanMachineData failed,err=[%v]", err)
			}
		}
	}
	return err
}

//从大磁盘机器复制数据到本地
func ScpData(remoteHost string, remotePath string, targetPath string) error {
	scpShell := fmt.Sprintf(" scp -i %v -o StrictHostKeyChecking=no  mysql@%v:%v  %v", DbbkRsaPath, remoteHost, remotePath, targetPath)
	_, _, err := SyncExecShell(scpShell)
	if err != nil {
		Log.Error("SyncExecShell scp failed,err=[%v],shell=[%v]", err, scpShell)
		return err
	}
	return nil
}

//从其他大磁盘机器复制数据到需要集中打包的机器
func ScpBinlogRemoteToLocalAndDelete(remoteHost string, remotePath string, targetPath string) error {
	scpShell := fmt.Sprintf(" scp -i %v -o StrictHostKeyChecking=no  mysql@%v:%v  %v && ssh -i %v -o StrictHostKeyChecking=no  mysql@%v \"rm -f %v\"",
		DbbkRsaPath, remoteHost, remotePath, targetPath, DbbkRsaPath, remoteHost, remotePath)
	_, _, err := SyncExecShell(scpShell)
	if err != nil {
		Log.Error("SyncExecShell scp failed,err=[%v],shell=[%v]", err, scpShell)
		return err
	}
	return nil
}

//从本地数据复制到大磁盘机器
func ScpBinlogToRemoteAndDelete(remoteHost string, remotePath string, targetPath string) error {
	scpShell := fmt.Sprintf("scp -i %v -o StrictHostKeyChecking=no  %v mysql@%v:%v && rm -f %v", DbbkRsaPath, targetPath, remoteHost, remotePath, targetPath)
	_, _, err := SyncExecShell(scpShell)
	if err != nil {
		Log.Error("SyncExecShell scp failed,err=[%v],shell=[%v]", err, scpShell)
		return err
	}
	return nil
}

//判断文件是否存在
func PathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

//随机生成字符串
func RandString(len int) string {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	bytes := make([]byte, len)
	for i := 0; i < len; i++ {
		b := r.Intn(26) + 65
		bytes[i] = byte(b)
	}
	return string(bytes)
}

//通过模板文件生成配置文件, template_filepath string 模板文件路径, new_filepath string 目标文件路径 ,替换模板中的数据
func GenerateConf(templateFile string, newFile string, data interface{}) error {
	var tmpl *template.Template
	tmpl, err := template.ParseFiles(templateFile) //从文件创建一个模板
	if err != nil {
		Log.Critical("create template error. err=[%v]", err)
		return err
	}
	file, err := os.Create(newFile)
	if err != nil {
		Log.Critical("open new file error. err=[%v]", err)
		return err
	}
	defer file.Close()

	writer := io.Writer(file)
	err = tmpl.Execute(writer, data)
	if err != nil {
		Log.Critical("open new file error. err=[%v]", err)
		return err
	}

	return nil
}

//获取本机IP
func GetLocalIp() (ip string, err error) {
	netInterfaces, err := net.Interfaces()
	if err != nil {
		Log.Warn("Fail to execute net.Interfaces(),reason=[%v]", err)
		return "", err
	}
	for i := 0; i < len(netInterfaces); i++ {
		if (netInterfaces[i].Flags & net.FlagUp) != 0 {
			addrs, _ := netInterfaces[i].Addrs()
			for _, address := range addrs {
				if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
					if ipnet.IP.To4() != nil {
						ip = ipnet.IP.String()
						return ip, nil
					}
				}
			}
		}
	}
	Log.Warn("No network IP available")
	err = errors.New("No IP obtained")
	return "", err
}
