# XtraBackup 工具库

这是一个基于 Percona XtraBackup 的 Go 语言工具库，提供了完整的 MySQL 备份和恢复功能。

## 功能特性

### 备份功能

- **全量备份**：完整的数据库备份
- **增量备份**：基于 LSN 的增量备份
- **流式备份**：支持 xbstream 格式的流式传输
- **远程备份**：通过 SSH 直接传输到远程服务器
- **压缩备份**：内置压缩功能，节省存储空间

### 恢复功能

- **备份准备**：apply-log 操作，准备备份数据
- **数据恢复**：move-back 操作，恢复数据到指定目录
- **增量恢复**：支持多个增量备份的依次应用
- **解压功能**：自动解压 xbstream 和压缩文件

### 控制功能

- **动态限速**：运行时调整备份速度
- **暂停/恢复**：支持备份过程的暂停和恢复
- **进程管理**：检查和控制备份进程
- **优雅停止**：安全停止备份操作

### 监控功能

- **进度监控**：实时监控备份进度
- **状态查询**：查看当前备份状态
- **回调机制**：自定义进度回调函数
- **日志记录**：详细的操作日志

## 快速开始

### 1. 基本配置

```go
config := &xtrabackup.Config{
    BinPath:         "bin/percona-xtrabackup-2.4.15-Linux-x86_64/bin/innobackupex",
    XbstreamPath:    "bin/percona-xtrabackup-2.4.15-Linux-x86_64/bin/xbstream",
    MySQLBaseDir:    "/home/<USER>/mysql",
    MySQLUser:       "backup_user",
    MySQLPassword:   "backup_password",
    MySQLSocket:     "/home/<USER>/mysql/tmp/mysql.sock",
    ThrottleRate:    100, // 100 MB/s 限速
    CompressLevel:   6,   // 压缩级别
    CompressThreads: 4,   // 压缩线程数
    UseMemory:       "5G", // apply-log 使用内存
    StreamFormat:    "xbstream",
    NoTimestamp:     true,
    SlaveInfo:       true,
}
```

### 2. 创建管理器

```go
manager := xtrabackup.NewManager(config)
```

### 3. 执行全量备份

```go
ctx := context.Background()
opts := &xtrabackup.BackupOptions{
    TargetDir:    "/backup/full/20241203",
    ExtraLSNDir:  "/tmp/extra_lsn_123",
    RemoteHost:   "backup-server",
    RemotePath:   "/remote/backup/full/20241203",
    SSHKeyPath:   "bin/.id_rsa_tmp_dbbk",
}

result, err := manager.FullBackup(ctx, opts)
if err != nil {
    log.Fatalf("Backup failed: %v", err)
}
fmt.Printf("Backup completed: LSN %s -> %s\n", result.LSNFrom, result.LSNTo)
```

### 4. 执行增量备份

```go
incOpts := &xtrabackup.BackupOptions{
    LSNPosition: result.LSNTo, // 使用全量备份的结束 LSN
    TargetDir:   "/backup/inc/20241204",
    ExtraLSNDir: "/tmp/extra_lsn_124",
}

incResult, err := manager.IncrementalBackup(ctx, incOpts)
```

### 5. 恢复备份

```go
restoreOpts := &xtrabackup.RestoreOptions{
    BackupPath:      "/backup/full/20241203",
    TargetDir:       "/restore/data",
    IncrementalDirs: []string{"/backup/inc/20241204"},
    UseMemory:       "5G",
    ParallelThreads: 16,
}

// 准备备份
prepareResult, err := manager.PrepareBackup(ctx, restoreOpts)

// 恢复数据
restoreResult, err := manager.RestoreBackup(ctx, restoreOpts)
```

## 高级功能

### 进度监控

```go
manager.monitor.SetCallback(func(info *xtrabackup.ProgressInfo) {
    fmt.Printf("Progress: %.2f%%, Status: %v, Speed: %d MB/s\n", 
        info.Progress, info.Status, info.Speed)
})
```

### 动态控制

```go
// 动态调整限速
manager.SetThrottleRate(200) // 调整为 200 MB/s

// 暂停备份
manager.Pause()

// 恢复备份
manager.Resume()

// 停止备份
manager.Stop()
```

### 状态查询

```go
// 检查是否正在运行
if manager.IsRunning() {
    fmt.Println("Backup is running")
}

// 获取当前状态
status := manager.GetStatus()
fmt.Printf("Current status: %v\n", status)

// 获取详细进度
progress := manager.GetProgress()
fmt.Printf("Progress: %.2f%%\n", progress.Progress)
```

## 配置选项

### Config 结构体

| 字段 | 类型 | 说明 |
|------|------|------|
| BinPath | string | innobackupex 二进制路径 |
| XbstreamPath | string | xbstream 二进制路径 |
| MySQLBaseDir | string | MySQL 基础目录 |
| MySQLUser | string | MySQL 用户名 |
| MySQLPassword | string | MySQL 密码 |
| MySQLSocket | string | MySQL socket 路径 |
| ThrottleRate | int64 | 限速 MB/s |
| CompressLevel | int | 压缩级别 (1-9) |
| CompressThreads | int | 压缩线程数 |
| UseMemory | string | apply-log 使用内存 |
| StreamFormat | string | 流格式 (xbstream) |
| NoTimestamp | bool | 不使用时间戳目录 |
| SlaveInfo | bool | 记录从库信息 |

### BackupOptions 结构体

| 字段 | 类型 | 说明 |
|------|------|------|
| Type | BackupType | 备份类型 (自动设置) |
| LSNPosition | string | LSN 位置 (增量备份用) |
| TargetDir | string | 目标目录 |
| ExtraLSNDir | string | 额外 LSN 目录 |
| RemoteHost | string | 远程主机 |
| RemotePath | string | 远程路径 |
| SSHKeyPath | string | SSH 密钥路径 |
| BackupOptions | string | 额外备份选项 |

## 错误处理

库提供了详细的错误信息和日志记录：

```go
result, err := manager.FullBackup(ctx, opts)
if err != nil {
    // 检查具体错误类型
    if strings.Contains(err.Error(), "already running") {
        fmt.Println("Another backup is already running")
    } else if strings.Contains(err.Error(), "failed to check backup process") {
        fmt.Println("Process check failed")
    }
    
    // 查看详细错误信息
    fmt.Printf("Backup failed: %v\n", err)
}
```

## 最佳实践

1. **备份前检查**：确保没有其他备份进程在运行
2. **合理设置限速**：根据业务负载调整限速参数
3. **监控进度**：使用回调函数监控备份进度
4. **错误处理**：妥善处理各种错误情况
5. **资源清理**：备份完成后及时清理临时文件
6. **并行复制管理**：库会自动管理并行复制设置

## 注意事项

- 确保 XtraBackup 二进制文件路径正确
- 备份用户需要有足够的权限
- 远程备份需要配置 SSH 密钥认证
- 增量备份依赖于基础备份的 LSN 信息
- 恢复操作会修改目标目录的数据

## 示例代码

完整的使用示例请参考 `example.go` 文件，包含：

- 基本备份和恢复流程
- 动态控制功能演示
- 进度监控示例
- 备份验证和解压示例
