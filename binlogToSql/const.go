package binlogtosql

//定义mysql数据类型，时间格式，sql操作类型
const (
	DATETIME_FORMAT      = "2006-01-02 15:04:05"
	SQL_TYPE_UNKNOW_TYPE = "unknowntype"
	SQL_TYPE_INT         = "int"
	SQL_TYPE_TINY_INT    = "tinyint"
	SQL_TYPE_SMALL_INT   = "smallint"
	SQL_TYPE_MEDIUM_INT  = "mediumint"
	SQL_TYPE_BIG_INT     = "bigint"
	SQL_TYPE_DECIMAL     = "decimal"
	SQL_TYPE_FLOAT       = "float"
	SQL_TYPE_DOUBLE      = "double"
	SQL_TYPE_BIT         = "bit"
	SQL_TYPE_DATETIME    = "datetime"
	SQL_TYPE_TIMESTAMP   = "timestamp"
	SQL_TYPE_TIME        = "time"
	SQL_TYPE_DATE        = "date"
	SQL_TYPE_YEAR        = "year"
	SQL_TYPE_ENUM        = "enum"
	SQL_TYPE_SET         = "set"
	SQL_TYPE_BLOB        = "blob"
	SQL_TYPE_VARCHAR     = "varchar"
	SQL_TYPE_CHAR        = "char"
	SQL_TYPE_JSON        = "json"
	SQL_TYPE_GEOMETRY    = "geometry"
	SQL_TYPE_TEXT        = "text"
	DML_TYPE_INSERT      = "insert"
	DML_TYPE_UPDATE      = "update"
	DML_TYPE_DELETE      = "delete"

	SQL_DELIMITER = ";\n"
)
