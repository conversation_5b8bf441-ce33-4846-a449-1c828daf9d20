package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"runtime"
	"sync"
	"syscall"
	"time"

	"google.golang.org/grpc"

	"mdc-agent/common"
	"mdc-agent/networker/receiver"
	"mdc-agent/worker"
)

var (
	BuildDate    string
	BuildVersion string
)

func main() {
	if err := common.InitControlLog(); nil != err {
		os.Stderr.WriteString(fmt.Sprintf("%v [mdc-agent start failed] Init control log failed. err=%v \n",
			time.Now().Format(common.TIME_FORMAT), err))
		os.Exit(-1)
	}

	//控制main使用cpu的总数,只用一颗cpu
	runtime.GOMAXPROCS(1)

	//传入配置文件
	conf := flag.String("config", "mdc-agent.yaml", "mdc agent configure file.")

	//解析传入的参数
	flag.Parse()
	fmt.Printf("Git commit:%s\n", BuildVersion)
	fmt.Printf("Build time:%s\n", BuildDate)

	// 配置和日志初始化，传入配置文件路径
	if err := common.ParseConfig(*conf); err != nil {
		os.Stderr.WriteString(fmt.Sprintf("%v [mdc-agent start failed]"+
			" Parse config file failed. err=%v \n", time.Now().Format(common.TIME_FORMAT), err))
		common.Log.Critical("mdc-agent init config failed, err=%v", err)
		os.Exit(-1)
	}

	wg := new(sync.WaitGroup)

	//初始化bnsIpList、初始化读写锁
	common.MdcServerIpList.RwLock = new(sync.RWMutex)
	if err := worker.UpdateIpList(); err != nil {
		os.Stderr.WriteString(fmt.Sprintf("%v [mdc-agent start failed]"+
			" Get ip list failed. err=%v \n", time.Now().Format(common.TIME_FORMAT), err))
		common.Log.Critical("mdc-agent get cs ip list failed. bns=[%v] err=[%v]",
			common.Config.MdcRpcServer.Addr, err)
		os.Exit(-1)
	}

	// 启动mdc-agent端grpc
	wg.Add(1)
	clientGrpcServer := receiver.InitRpcServer()
	go receiver.StartAgentServer(wg, clientGrpcServer)
	common.Log.Debug("mdc-agent starting grpc listener is finish .")

	//启动woker
	wg.Add(1)
	go worker.StartWorker(common.Config.WorkerNumber, common.Config.WorkerChanTimeOut, wg)
	//解析bns
	wg.Add(1)
	go worker.StartAnalysisBns(wg)
	// 接收signal命令
	wg.Add(1)
	go signalHandle(wg, clientGrpcServer)
	fmt.Printf("Start mdc-agent successful. \n")
	os.Stdout.WriteString(fmt.Sprintf("%v [mdc-agent start success] \n", time.Now().Format(common.TIME_FORMAT)))

	//等待所有工作协程处理完已有请求，优雅退出
	wg.Wait()

}

// 接收signal命令后优雅退出
func signalHandle(wg *sync.WaitGroup, grpcServer *grpc.Server) {
	sc := make(chan os.Signal)
	signal.Notify(sc, syscall.SIGINT, syscall.SIGTERM)
	for {
		switch sig := <-sc; sig {
		case syscall.SIGINT, syscall.SIGTERM:
			os.Stdout.WriteString(fmt.Sprintf("%v [mdc-agent receive exit signal]"+
				" Signal=%v \n", time.Now().Format(common.TIME_FORMAT), sig))
			common.Log.Notice("receive exit signal:%v ,mdc-agent start to exit", sig)
			//关闭rpc server
			receiver.StopAgentServer(wg, grpcServer)
			//关闭worker
			worker.StopWorker()
			//关闭mdc-server bns探测
			worker.StopAnalysisBns()
			// 关闭OS信号量接收
			wg.Done()
			return
		default:
			os.Stderr.WriteString(fmt.Sprintf("%v [mdc-agent receive unknown signal]"+
				" Signal=%v \n", time.Now().Format(common.TIME_FORMAT), sig))
			common.Log.Warn("receive unknown signal:%v", sig)
		}
	}
}
