package receiver

import (
	"context"
	"errors"
	"fmt"
	"net"
	"os"
	"sync"
	"sync/atomic"
	"time"

	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"
	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"
	"mdc-agent/common"
	"mdc-agent/networker/worker"
)

type MDCAgentServer struct {
	pb_agent.MdcAgentServer
}

// receiver参数初始化
func InitRpcServer() *grpc.Server {
	var opts []grpc.ServerOption

	//初始化rpc server连接参数
	var server_opts []grpc.ServerOption

	//keepalive参数定义
	server_opts = append(server_opts,
		grpc.KeepaliveParams(keepalive.ServerParameters{
			MaxConnectionIdle: time.Duration(common.Config.RpcKeepalive.RpcServerParam.MaxConnectionIdle) * time.Second,
			Time:              time.Duration(common.Config.RpcKeepalive.RpcServerParam.Time) * time.Second,
			Timeout:           time.Duration(common.Config.RpcKeepalive.RpcServerParam.Timeout) * time.Second}),
		grpc.KeepaliveEnforcementPolicy(keepalive.EnforcementPolicy{
			MinTime:             time.Duration(common.Config.RpcKeepalive.EnforcementPolicy.MinimumTime) * time.Second,
			PermitWithoutStream: common.Config.RpcKeepalive.EnforcementPolicy.PermitWithoutStream}))

	return grpc.NewServer(opts...)
}

// 启动mdc Agent rpc server
func StartAgentServer(waitGroup *sync.WaitGroup, grpcServer *grpc.Server) {
	//结构体初始化，一定会成功
	proxyAgentListener, err := net.Listen("tcp4", fmt.Sprintf("0.0.0.0:%d", common.Config.TaskListenPort))
	if err != nil {
		common.Log.Critical("start task listen port failed, err=%v", err)
		os.Exit(-1)
	}

	//注册rpc server
	pb_agent.RegisterMdcAgentServer(grpcServer, &MDCAgentServer{})
	err = grpcServer.Serve(proxyAgentListener)
	if err != nil {
		common.Log.Critical("failed to start proxy agent listener, err=%v", err)
		StopAgentServer(waitGroup, grpcServer)
		os.Exit(-1)
	}
}

// 优雅关闭mdc Agent rpc server
func StopAgentServer(waitGroup *sync.WaitGroup, grpcServer *grpc.Server) {
	//关闭rpc Server
	//等待已发起的的rpc请求处理完毕后再关闭
	grpcServer.GracefulStop()
	//如果消息队列还未关闭，则关闭消息队列
	if atomic.LoadInt32(&common.AgentMsgChanIsClose) == 0 {
		atomic.AddInt32(&common.AgentMsgChanIsClose, 1)
		//关闭全局Server消息队列
		close(common.AgentMsgChan)
	}

	waitGroup.Done()
}

//接收来自mdc-server的同步任务
func (server *MDCAgentServer) ReceiveSyncTaskMsgFromServer(ctx context.Context, taskMsg *pb_agent.MdcAgentSyncMsg) (res *pb_agent.MdcAgentSyncRespons, err error) {
	taskId := taskMsg.GetTaskId()
	taskType := taskMsg.MdcAgentTaskType
	res = &pb_agent.MdcAgentSyncRespons{
		TaskId:           taskId,
		MdcAgentTaskType: taskType,
	}
	common.Log.Notice("MsMessage received successfully, TaskId=[%v], taskType=[%v],taskMsg=[%v]", taskMsg.TaskId, taskType, taskMsg.GetMsgType())
	switch taskMsg.MdcAgentTaskType {
	case pb_server.MdcAgentTaskType_CHECK_STATUS:
		//检查主备状态
		response, err := worker.HandleCheckBkStatus(taskMsg)
		if err != nil {
			common.Log.Warn("Failed to check bkMysql status, taskId=[%v], err=[%v]", taskMsg.TaskId, err)
			res.Msg = err.Error()
		} else {
			res.Msg = ""
			res.MsgType = &pb_agent.MdcAgentSyncRespons_CheckBkMysqlStatusRes{
				CheckBkMysqlStatusRes: response,
			}
		}
		return res, err
	case pb_server.MdcAgentTaskType_GET_FREE_CAP:
		//获得机器空闲容量
		response, err := worker.HandleGetFreeCap(taskMsg)
		if err != nil {
			common.Log.Warn("Failed to get free cap, taskId=[%v], err=[%v]", taskMsg.TaskId, err)
			res.Msg = err.Error()
		} else {
			//respons.ErrorCode = tinkerpb.ErrorCode_SUCCESSFUL
			res.Msg = ""
			res.MsgType = &pb_agent.MdcAgentSyncRespons_GetFreeStorageRes{
				GetFreeStorageRes: response,
			}
		}
		return res, err
	case pb_server.MdcAgentTaskType_STOP_XTRA:
		//关闭xtra备份
		err := worker.HandleStopXtraTask(taskMsg)
		if err != nil {
			common.Log.Warn("Failed to stop xtra task, taskId=[%v], err=[%v]", taskMsg.TaskId, err)
			res.Msg = err.Error()
		} else {
			//respons.ErrorCode = tinkerpb.ErrorCode_SUCCESSFUL
			res.Msg = ""
		}
		return res, err
	case pb_server.MdcAgentTaskType_EXEC_START_BINLOG:
		//开启binlog备份
		response, err := worker.HandleStartBinlogTask(taskMsg)
		if err != nil {
			common.Log.Warn("Failed to stop xtra task, taskId=[%v], err=[%v]", taskMsg.TaskId, err)
			res.Msg = err.Error()
		} else {
			//respons.ErrorCode = tinkerpb.ErrorCode_SUCCESSFUL
			res.Msg = ""
			res.MsgType = &pb_agent.MdcAgentSyncRespons_StartBinlogRes{
				StartBinlogRes: response,
			}
		}
		return res, err
	case pb_server.MdcAgentTaskType_EXEC_STOP_BINLOG:
		//关闭binlog备份
		err := worker.HandleStopBinlogTask(taskMsg)
		if err != nil {
			common.Log.Warn("Failed to stop binlog backup, taskId=[%v], err=[%v]", taskMsg.TaskId, err)
			res.Msg = err.Error()
		} else {
			//respons.ErrorCode = tinkerpb.ErrorCode_SUCCESSFUL
			res.Msg = ""
		}
		return res, err
	case pb_server.MdcAgentTaskType_GET_BKMYSQL_INFO:
		//获得备库的账号密码
		response, err := worker.HandleGetBkMysqlInfo(taskMsg)
		if err != nil {
			common.Log.Warn("Failed to get bkMysql info, taskId=[%v], err=[%v]", taskMsg.TaskId, err)
			res.Msg = err.Error()
		} else {
			res.Msg = ""
			res.MsgType = &pb_agent.MdcAgentSyncRespons_GetBkMysqlInfoRes{
				GetBkMysqlInfoRes: response,
			}
		}
		return res, err
	case pb_server.MdcAgentTaskType_CLEAN_DATA:
		//清理数据
		err = worker.HandleCleanData(taskMsg)
		if err != nil {
			common.Log.Warn("Failed to clean data, taskId=[%v], err=[%v]", taskMsg.TaskId, err)
			res.Msg = err.Error()
		} else {
			//respons.ErrorCode = tinkerpb.ErrorCode_SUCCESSFUL
			res.Msg = ""
		}
		return res, err
	case pb_server.MdcAgentTaskType_DELETE_BINLOGBK:
		//删除binlog备份
		err = worker.HandleDeleteBinlogBk(taskMsg)
		if err != nil {
			common.Log.Warn("Failed to delete remote binlog, taskId=[%v], err=[%v]", taskMsg.TaskId, err)
			res.Msg = err.Error()
		} else {
			//respons.ErrorCode = tinkerpb.ErrorCode_SUCCESSFUL
			res.Msg = ""
		}
		return res, err
	case pb_server.MdcAgentTaskType_GRANT_INSTANCE_PRIVILEGES:
		err = worker.HandleGrantPrivileges(taskMsg)
		if err != nil {
			common.Log.Warn("Failed to GRANT_INSTANCE_PRIVILEGES. err=[%v]", err)
			res.Msg = err.Error()
		} else {
			//respons.ErrorCode = tinkerpb.ErrorCode_SUCCESSFUL
			res.Msg = ""
		}
		return res, err
	default:
		errMsg := fmt.Sprintf("Unknown task type,taskType=[%v]", taskMsg.MdcAgentTaskType)
		return nil, errors.New(errMsg)
	}
}

func (server *MDCAgentServer) ReceiveAsyncTaskMsgFromServer(ctx context.Context, taskMsg *pb_agent.MdcAgentAsyncMsg) (*pb_agent.MdcAgentAsyncRespons, error) {
	//将消息放到入mdc-server消息队列中
	if err := PutMsMsgToChan(taskMsg); err != nil {
		common.Log.Error("[mdc-Agent] Fail to put mysql agent "+
			" message to MsgChan,TaskId=[%v], error=[%v]", taskMsg.TaskId, err)
		return nil, err
	}
	common.Log.Notice("MsMessage received successfully,TaskId=[%v],MsgInfo=[%v]", taskMsg.TaskId, taskMsg)
	resposMsg := pb_agent.MdcAgentAsyncRespons{
		TaskId:      taskMsg.GetTaskId(),
		ResponsNote: fmt.Sprintf("Receiver had received  Message TaskId=[%v]", taskMsg.TaskId)}
	return &resposMsg, nil
}

//将任务写入消息队列 交由work协程处理
func PutMsMsgToChan(msMsg *pb_agent.MdcAgentAsyncMsg) error {
	//如果消息队列已满，则打印warning日志
	if cap(common.AgentMsgChan) == len(common.AgentMsgChan) {
		err := errors.New("MdcAgent MsgChan buffer is full.")
		common.Log.Warning("MDC-Agent Warning Info=[%v]. MsServerMsgChan cap=[%v] message context=[%v]", err, cap(common.AgentMsgChan), msMsg)
	}
	//如果消息队列没有关闭，则写入
	if atomic.LoadInt32(&common.AgentMsgChanIsClose) == 0 {
		common.AgentMsgChan <- msMsg
	} else {
		err := errors.New("MdcAgent MsgChan is closed.")
		common.Log.Warning("MDC-Agent Warning Info=[%v].common.MsServerMsgChanIsClose=[%v],message context=[%v]",
			err, atomic.LoadInt32(&common.AgentMsgChanIsClose), msMsg)
		return err
	}
	return nil
}
