package worker

import (
	"sync"
	"sync/atomic"
	"time"

	"mdc-agent/common"
	"mdc-agent/networker/worker"
)

// 启动工作协程
func StartWorker(threadNum int, chanTimeout int, mWaitGroup *sync.WaitGroup) {
	wg := new(sync.WaitGroup)
	for idx := 0; idx < threadNum; idx++ {
		wg.Add(1)
		go workRoutine(idx, chanTimeout, wg)
	}

	wg.Wait()
	mWaitGroup.Done()
}

// 退出工作协程
func StopWorker() {
	if atomic.LoadInt32(&common.AgentMsgChanIsClose) == 0 {
		atomic.AddInt32(&common.AgentMsgChanIsClose, 1)
		//关闭全局DsAgent消息队列
		close(common.AgentMsgChan)
	}
}

// 每个工作协程的处理函数
func workRoutine(idx int, chanTimeout int, wg *sync.WaitGroup) {
	for {
		select {
		case msg, ok := <-common.AgentMsgChan:
			if ok {
				//消息处理函数
				err := worker.ExecuteTasks(msg)
				if err != nil {
					common.Log.Notice("receive dsMsg=%v from server", msg)
				}
			} else {
				//任务channel已关闭，工作协程正常退出
				wg.Done()
				return
			}
		case <-time.After(time.Second * time.Duration(chanTimeout)):
			//common.Log.Notice("dsWorker[%d] routine wait timeout,current channel length=[%d],cap=[%d]", idx, len(networker.ServerMsgChan), cap(networker.ServerMsgChan))
		}
	}
}
