package binlogtosql

import (
	"errors"
	"fmt"
	"os"

	"mdc-agent/common"

	SQL "github.com/dropbox/godropbox/database/sqlbuilder"
	"github.com/go-mysql-org/go-mysql/replication"
)

// GenericSQLs
// 循环从队列中监听Event生成SQL语句写入解析文件
func GenericSQLs(ctx *Configs, fileName string) error {
	// 创建解析文件
	f, err := os.Create(fileName)
	if err != nil {
		errMsg := fmt.Sprintf("cate parse sql file failed, err=[%v]", err)
		common.Log.Error(errMsg)
		return errors.New(errMsg)
	}

	defer f.Close()

	for {
		select {
		case event, ok := <-ctx.EventChan:
			if ok {
				sqlsFromOneRowEvent, err := GenericSQLsFromOneRowEvent(ctx, event)
				if err != nil {
					common.Log.Warning("ErrorMsg=[generic sqls error] err=[%v]", err)
					return err
				}
				for _, sqlInfo := range sqlsFromOneRowEvent {
					f.Write([]byte(sqlInfo))
				}
			}
		case done, ok := <-ctx.StatusChan:
			if ok && done {
				if len(ctx.EventChan) == 0 {
					return err
				} else {
					ctx.StatusChan <- true
				}
			}
		}
	}
}

// GenericSQLsFromOneRowEvent
//根据个RowEvent生成对应的SQL语句
func GenericSQLsFromOneRowEvent(ctx *Configs, event *replication.BinlogEvent) ([]string, error) {
	var (
		sqls         []string
		colNames     []ColumnInfo
		colsDef      []SQL.NonAliasColumn
		colsTypeName []string
		err          error
	)
	rowEvent := event.Event.(*replication.RowsEvent)
	schema, table, sqlType := getTbDbSQLTypeFromEvent(event, rowEvent)
	tableInfo, err := GetTbInfo(ctx, schema, table)
	if err != nil {
		common.Log.Warning("ErrorMsg=[generic sql stopped due to get table info error] err=[%v]", err)
		return nil, err
	}
	colNames = tableInfo.columns
	if len(rowEvent.Rows) == 0 {
		common.Log.Warning("ErrorMsg=[generic sql stopped due to nil row event]")
		return nil, fmt.Errorf("get invalid row event whithout row data")
	}
	//如果binlog中记录到的字段数和查表返回到的字段数不同，则发生了DDL增加或减少了字段数，直接返回
	if len(rowEvent.Rows[0]) != len(colNames) {
		common.Log.Warning("ErrorMsg=[generic sql stopped due to some DDL] table=[%v] err=[%v]", fmt.Sprintf("%v.%v", schema, table), err)
		return nil, fmt.Errorf("some DDL happens in `%v`.`%v`", schema, table)
	}
	colsDef, colsTypeName = getSQLColumnExpressions(len(rowEvent.Rows[0]), colNames, rowEvent.Table)
	//通过比较rowEvent和查询到的表信息判断表结构有没有发生改变,此处只能判断修改字段类型，修改字段名称则无法识别
	if checkDDLChanges(colNames, colsTypeName) {
		common.Log.Warning("ErrorMsg=[DDL happens in table] schema=[%v] table=[%v] err=[%v]", schema, table, err)
		return nil, fmt.Errorf("some DDL happens in `%v`.`%v`", schema, table)
	}
	switch sqlType {
	case DML_TYPE_INSERT:
		sqls, err = GenericInsertSQLFromOneRowEvent(rowEvent, colsDef)
		if err != nil {
			common.Log.Warning("ErrorMsg=[fail to generate insert sql] rowEvent=[%v] err=[%v]", rowEvent, err)
			return nil, err
		}
	case DML_TYPE_UPDATE:
		sqls, err = GenericUpdateSQLFromOneRowEvent(rowEvent, colsTypeName, colsDef, tableInfo.primarykeyMap)
		if err != nil {
			common.Log.Warning("ErrorMsg=[fail to generate update sql] rowEvent=[%v] err=[%v]", err)
			return nil, err
		}
	case DML_TYPE_DELETE:
		sqls, err = GenericDeleteSQLFromOneRowEvent(rowEvent, colsDef, tableInfo.primarykeyMap)
		if err != nil {
			common.Log.Warning("ErrorMsg=[fail to generate delete sql] rowEvent=[%v] err=[%v]", err)
			return nil, err
		}
	}
	return sqls, nil
}

//GenericDeleteSQLFromOneRowEvent
//生成delete语句
func GenericDeleteSQLFromOneRowEvent(rowEvent *replication.RowsEvent, colsDef []SQL.NonAliasColumn, primaryKey []int) ([]string, error) {
	var (
		oneSQL     string
		err        error
		newColDefs []SQL.NonAliasColumn = colsDef[:]
		rowCnt     int                  = len(rowEvent.Rows)
		sqls       []string             = make([]string, rowCnt)
		table      string               = string(rowEvent.Table.Table)
		schema     string               = string(rowEvent.Table.Schema)
	)
	for i := 0; i < rowCnt; i++ {
		//生成where子句
		whereCondition := genEqualConditions(rowEvent.Rows[i], newColDefs, primaryKey)
		//(由table schema column where)获得完整的delete语句
		oneSQL, err = SQL.NewTable(table, newColDefs...).Delete().Where(SQL.And(whereCondition...)).String(schema)
		if err != nil {
			common.Log.Warning("ErrorMsg=[fail to generate delete sql] err=[%v]", err)
			return nil, err
		}
		sqls[i] = fmt.Sprintf("%v%v", oneSQL, SQL_DELIMITER)
	}
	return sqls, err
}

//GenericInsertSQLFromOneRowEvent
//生成Insert语句
func GenericInsertSQLFromOneRowEvent(rowEvent *replication.RowsEvent, colsDef []SQL.NonAliasColumn) ([]string, error) {
	var (
		insertSql  SQL.InsertStatement
		oneSQL     string
		err        error
		newColDefs []SQL.NonAliasColumn = colsDef[:]
		rowCnt     int                  = len(rowEvent.Rows)
		schema     string               = string(rowEvent.Table.Schema)
		table      string               = string(rowEvent.Table.Table)
		sqls       []string
	)
	for i := 0; i < rowCnt; i++ {
		//用表名和列信息初始化insertSQL结构体
		insertSql = SQL.NewTable(table, newColDefs...).Insert(newColDefs...)
		//将原始数据转换成sqlbuilder包中的转义字符串，对应values(...)部分
		valuesInserted := convertRowToExperssionRow(rowEvent.Rows[i])
		//组合value部分
		insertSql.Add(valuesInserted...)
		oneSQL, err = insertSql.String(schema)
		if err != nil {
			common.Log.Warning("ErrorMsg=[fail to generate insert sql] err=[%v]", err)
			return nil, err
		} else {
			sqls = append(sqls, fmt.Sprintf("%v%v", oneSQL, SQL_DELIMITER))
		}
	}
	return sqls, err
}

//GenericUpdateSQLFromOneRowEvent
//生成Update语句
func GenericUpdateSQLFromOneRowEvent(
	rowEvent *replication.RowsEvent, colsTypeNames []string,
	colDefs []SQL.NonAliasColumn, primarykey []int) ([]string, error) {
	var (
		rowCnt    int    = len(rowEvent.Rows)
		schema    string = string(rowEvent.Table.Schema)
		table     string = string(rowEvent.Table.Table)
		oneSql    string
		sqls      []string
		wherePart []SQL.BoolExpression
		err       error
	)
	//update事件在binlog中记录为两行，第一行为原始数据，第二行为新数据
	for i := 0; i < rowCnt; i += 2 {
		//用表名和列信息初始化updateSQL结构体
		updateSQL := SQL.NewTable(table, colDefs...).Update()
		//生成set部分
		updateSQL = genUpdateSetPart(updateSQL, rowEvent.Rows[i], rowEvent.Rows[i+1], colDefs, colsTypeNames)
		//生成where部分
		wherePart = genEqualConditions(rowEvent.Rows[i+1], colDefs, primarykey)
		//组合where部分
		updateSQL.Where(SQL.And(wherePart...))
		//调用string接口获得的update语句
		oneSql, err = updateSQL.String(schema)
		if err != nil {
			common.Log.Warning("ErrorMsg=[fail to generate update sql] err=[%v]", err)
			return nil, err
		} else {
			sqls = append(sqls, fmt.Sprintf("%v%v", oneSql, SQL_DELIMITER))
		}
	}
	return sqls, nil
}

//getTbDbSQLTypeFromEvent
//子函数，用来得到当前event对应的库名，表明，类型(insert update delete)
func getTbDbSQLTypeFromEvent(event *replication.BinlogEvent, rowEvent *replication.RowsEvent) (string, string, string) {
	var (
		schema  string
		table   string
		sqlType string
	)
	schema = string(rowEvent.Table.Schema)
	table = string(rowEvent.Table.Table)
	switch event.Header.EventType {
	case
		replication.WRITE_ROWS_EVENTv0,
		replication.WRITE_ROWS_EVENTv1,
		replication.WRITE_ROWS_EVENTv2:
		sqlType = DML_TYPE_INSERT
	case
		replication.UPDATE_ROWS_EVENTv0,
		replication.UPDATE_ROWS_EVENTv1,
		replication.UPDATE_ROWS_EVENTv2:
		sqlType = DML_TYPE_UPDATE
	case
		replication.DELETE_ROWS_EVENTv0,
		replication.DELETE_ROWS_EVENTv1,
		replication.DELETE_ROWS_EVENTv2:
		sqlType = DML_TYPE_DELETE
	}
	return schema, table, sqlType
}
