# mdc-agent需求澄清

mdc-agent部署在每一台mysql机器上。容器化部署时，mdc-agent默认打包进mysql镜像中，并在容器启动时拉取最新版本的mdc-agent

## 功能设计

### 1 数据备份

#### 1.1 备份内容

- 数据库表全量备份

- binlog增量备份

#### 1.2 BOS存储

- 本机临时存储

- 华北BOS短期存储

- 华南BOS长期存储

### 2 数据回放

接收参数：

- 【必填】mysql的部署目录，绝对路径
- 【必填】全量库表下载地址
- 【选填】binlog下载地址（列表）

数据恢复步骤：

1. 下载全量库表数据
2. 解压全量库表数据
3. 移动数据到mysql部署目录
4. 
启动mysql实例
4. 下载binlog数据
5. 解压binlog数据
6. 恢复binlog数据

全量恢复库表数据

指定实例进行回放

1. mysqldump全量覆盖
2. binlog增量恢复

## 附：阿里云能力

[SQL备份与回滚（公测中）](https://help.aliyun.com/zh/dms/sql-backup-and-rollback)

对比项 | 数据追踪 | SQL备份与回滚
---|---|---
功能使用 | 需要提交数据追踪工单，配置时间范围、表名、操作类型等工单参数。| 无需进行额外的工单配置。仅需在执行SQL时，开启备份，待获取备份数据后，直接在SQL Console执行即可。
回滚精确度 | 需要在一段时间范围内筛选出符合表的变更条件的SQL，再从中寻找目标。 | 针对MySQL数据库可精确定位到误操作的SQL，直接生成回滚脚本。非MySQL的关系型数据库需要手动回滚数据。
实例的能力要求 | 不限制实例的能力。 | 实例需要具有自由操作（有5次试用次数）和稳定变更能力（不限制使用次数）。
