package worker

import (
	"fmt"

	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"

	"mdc-agent/common"
	"mdc-agent/networker/send"
)

//识别任务类型分配到具体任务函数执行
func ExecuteTasks(taskMsg *pb_agent.MdcAgentAsyncMsg) error {
	taskId := taskMsg.GetTaskId()
	taskType := taskMsg.MdcAgentTaskType
	res := &pb_server.MdcAgentReportMsg{
		Taskid:           taskId,
		BaseMessage:      taskMsg.BaseMessage,
		MdcAgentTaskType: taskType,
		ErrorMsg:         "",
	}
	common.Log.Notice("ExecuteTasks taskId=[%v] taskType=[%v] task=[%v]", taskId, taskType, taskMsg)
	switch taskMsg.MdcAgentTaskType {
	case pb_server.MdcAgentTaskType_START_XTRA:
		response, err := HandlExecxtra(taskMsg)
		if err != nil {
			errMsg := fmt.Sprintf("HandlExecExecxtra failed. err=[%v]", err)
			common.Log.Error(errMsg)
			res.ErrorMsg = errMsg
		} else if response != nil {
			common.Log.Info("xtrabackup success report to mdc-server. report=[%+v]", *response)
			res.MsgType = &pb_server.MdcAgentReportMsg_ExecXtrabkReport{
				ExecXtrabkReport: response,
			}
		}
	case pb_server.MdcAgentTaskType_UNPACK_DATA:
		response, err := HandleUnpackData(taskMsg)
		if err != nil {
			errMsg := fmt.Sprintf("HandleUnpackData failed,err=[%v]", err)
			common.Log.Error(errMsg)
			res.ErrorMsg = errMsg
		} else if response != nil {
			res.MsgType = &pb_server.MdcAgentReportMsg_UnpackDataReport{
				UnpackDataReport: response,
			}
		}
	case pb_server.MdcAgentTaskType_UPLOADDBABOS:
		response, err := HandleUpLoadDbaBos(taskMsg)
		if err != nil {
			errMsg := fmt.Sprintf("HandleUpLoadDbaBos failed,err=[%v]", err)
			common.Log.Error(errMsg)
			res.ErrorMsg = errMsg
		} else if response != nil {
			res.MsgType = &pb_server.MdcAgentReportMsg_UploadDBABOSReport{
				UploadDBABOSReport: response,
			}
			common.Log.Info("updload dba bos success. response=[%+v]", *response)
		}
	case pb_server.MdcAgentTaskType_DOWNLOAD_RS_DATA:
		response, err := HandleDownloadRestoreData(taskMsg)
		if err != nil {
			errMsg := fmt.Sprintf("HandleDownloadRestoreData failed,err=[%v]", err)
			common.Log.Error(errMsg)
			res.ErrorMsg = errMsg
		} else if response != nil {
			res.MsgType = &pb_server.MdcAgentReportMsg_DownLoadRsDataReport{
				DownLoadRsDataReport: response,
			}
		}
	case pb_server.MdcAgentTaskType_DOWNLOAD_BINLOG_DATA:
		response, err := HandleDownloadBinlogData(taskMsg)
		if err != nil {
			errMsg := fmt.Sprintf("HandleDownloadBinlogData failed,err=[%v]", err)
			common.Log.Error(errMsg)
			res.ErrorMsg = errMsg
		} else if response != nil {
			res.MsgType = &pb_server.MdcAgentReportMsg_DownLoadRsBinlogDataReport{
				DownLoadRsBinlogDataReport: response,
			}
		}
	case pb_server.MdcAgentTaskType_EXEC_RESTORE:
		response, err := HandleExecRestore(taskMsg)
		if err != nil {
			errMsg := fmt.Sprintf("HandleExecRestore failed,err=[%v]", err)
			common.Log.Error(errMsg)
			res.ErrorMsg = errMsg
		} else if response != nil {
			res.MsgType = &pb_server.MdcAgentReportMsg_ExecRestoreReport{
				ExecRestoreReport: response,
			}
		}
	case pb_server.MdcAgentTaskType_EXEC_PARSE_BINLOG:
		response, err := HandleExecParseBinlog(taskMsg)
		if err != nil {
			errMsg := fmt.Sprintf("HandleExecParseBinlog failed,err=[%v]", err)
			common.Log.Error(errMsg)
			res.ErrorMsg = errMsg
		} else if response != nil {
			res.MsgType = &pb_server.MdcAgentReportMsg_ExecParseBinlogReport{
				ExecParseBinlogReport: response,
			}
		}
	case pb_server.MdcAgentTaskType_CHECK_BINLOGSTATUS:
		//检查binlog备份进程
		response, err := HandleCheckBinlogStatus(taskMsg)
		if err != nil {
			errMsg := fmt.Sprintf("HandleCheckBinlog failed, err=[%v]", err)
			common.Log.Error(errMsg)
			common.Log.Warn("Failed to check binlog status, taskId=[%v], err=[%v]", taskMsg.TaskId, err)
			res.ErrorMsg = errMsg
		} else if response != nil {
			res.MsgType = &pb_server.MdcAgentReportMsg_CheckBinlogStatusReport{
				CheckBinlogStatusReport: response,
			}
		}
	case pb_server.MdcAgentTaskType_EXEC_PARTITION_RESTORE:
		if err := RestorePartitionData(taskMsg); err != nil {
                       res.ErrorMsg = fmt.Sprintf("%v", err)
		}
	default:
		errMsg := fmt.Sprintf("Unknown task type,taskType=[%v]", taskMsg.MdcAgentTaskType)
		common.Log.Error(errMsg)
		res.ErrorMsg = errMsg
	}
	ip, err := common.GetLocalIp()
	if err != nil {
		errMsg := fmt.Sprintf(" ExecuteTasks GetLocalIp failed,err=[%v]", err)
		common.Log.Error(errMsg)
		res.ErrorMsg = errMsg
	} else {
		res.ClientIp = ip
	}
	if err = send.SendReportMsgToServer(res); err != nil {
		return err
	}
	return nil
}
