package common

import (
	"errors"
	"fmt"
	"io/ioutil"
	"math"
	"os"
	"time"

	"gopkg.in/yaml.v2"
)

// control log日志打印时间格式
const TIME_FORMAT = "2006-01-02 15:04:05"

var BaseDir string

type Configuration struct {
	// +++++++++++++++日志相关+++++++++++++++++
	// 日志级别，这里使用了 beego 的 log 包
	// [0:Emergency, 1:Alert, 2:Critical, 3:Error, 4:Warning, 5:Notice, 6:Informational, 7:Debug]
	LogLevel int `yaml:"log-level"`
	// 日志输出位置，默认日志输出到控制台
	// 目前只支持['console', 'file']两种形式，如非console形式这里需要指定文件的路径，可以是相对路径
	LogOutput string `yaml:"log-output"`
	// 日志最大保留天数
	LogMaxDays int `yaml:"log-maxdays"`
	// 服务端消息chan长度
	ServerMsgChanBufferLength int `yaml:"server-msgchan-bufferlength"`
	//mdc agent监听server发送任务端口
	TaskListenPort int `yaml:"task-listen-port"`
	// +++++++++++++++grpc消息传递相关+++++++++++++++++
	MdcRpcServer      *Rpcdsn       `yaml:"mdc-rpc-server"` // rpc server配置
	ServerRWTimeout   int           `yaml:"server_rw_timeout"`
	ServerConnTimeout int           `yaml:"server_conn_timeout"`
	BnsReloadCycle    int           `yaml:"bns_reload_cycle"`     //白名单或整个配置文件自动热加载周期，单位s
	RpcKeepalive      *rpckeepalive `yaml:"server-rpc-keepalive"` // rpc-keepalive配置
	//++++++++++++++++++work相关++++++++++++++++++++++++
	WorkerNumber       int `yaml:"worker-number"`
	WorkerChanTimeOut  int `yaml:"worker-chan-timeout"`
	AnalysisBnsTimeout int `yaml:"analysis-bns-timeout"`

	// +++++++++++++++bos配置相关+++++++++++++++++
	DbaBosBucket           string `yaml:"dba_bos_bucket"`
	DbaBosAccessKeyID      string `yaml:"dba_bos_accesskeyid"`
	DbaBosAccessKeySecret  string `yaml:"dba_bos_accesskeysecret"`
	DbaBosEndPoint         string `yaml:"dba_bos_endpoint"`
	SiodBosBucket          string `yaml:"siod_bos_bucket"`
	SiodBosAccessKeyID     string `yaml:"siod_bos_accesskeyid"`
	SiodBosAccessKeySecret string `yaml:"siod_bos_accesskeysecret"`
	SiodBosEndPoint        string `yaml:"siod_bos_endpoint"`
	MysqlNormalPackage     string `yaml:"mysql-normal-package"` //安装包下载路径配置
	MysqlFdbPackage        string `yaml:"mysql-fdb-package"`
	BosRetryCount          int    `yaml:"bos-retry-count"`
}

var Config = &Configuration{
	MdcRpcServer: &Rpcdsn{
		Addr: "0.0.0.0",
		Port: 10000,
		//http连接创建时握手交互超时时间
		ConnectionTimeout: 35,
	},
	LogLevel:   3,
	LogOutput:  "log/mysql-agent.log",
	LogMaxDays: 7,

	AnalysisBnsTimeout:        1,
	ServerMsgChanBufferLength: 1000,
	ServerConnTimeout:         5,
	ServerRWTimeout:           5,

	//keepalive配置
	//一个连接可被多次rpc交互复用，以下配置决定这个连接什么时候断开
	RpcKeepalive: &rpckeepalive{
		RpcClientParam: &keepaliveclientparam{
			//如果在该时间内，client未发现活跃连接，则对server发送ping包探活
			//最小设置10s，小于10s则自动设置为1分钟
			Time: 120,
			//从keepalive ping包发起时间点开始timeout时间内如果未发生rpc交互，则判定连接为关闭状态
			TimeOut: 35,
			//如果设置为true，即使没有活跃的rpc请求，也依然做连接探活
			//如果设置为false，如果没有活跃的rpc请求，则不做连接探活 =>出于资源消耗考虑选false
			PermitWithOutStream: false,
		},
		RpcServerParam: &keepaliveserverparam{
			//连接最大空闲时长，超过则关闭连接
			MaxConnectionIdle: 30,
			//连接最大保持时长，这个值会上下浮动10%后进行传播
			//官方原文：connection may exist before it will be closed by sending a GoAway.
			// Arandom jitter of +/-10% will be added to MaxConnectionAge to spread out
			// connection storms.
			MaxConnectionAge: math.MaxInt64,
			//链接达到MaxConnectionAge的时间后，会在MaxConnectionAgeGrace范围内被强制关闭
			MaxConnectionAgeGrace: math.MaxInt64,
			//如果在该时间内，server未发现活跃连接，则对client发送ping包探活
			//最小设置10s，小于10s则自动设置为1分钟
			Time: 120,
			//client端ping超时等待时间，即使没有活跃连接或连接已经关闭，也依然等待这个时间走完。
			Timeout: 35,
		},
		EnforcementPolicy: &keepaliveenforcementpolicy{
			//client端发出探活包的最小等待时间
			MinimumTime: 100,
			//如果设置为true，即使没有活跃的rpc请求，也依然做连接探活
			//如果设置为false，如果没有活跃的rpc请求，client端发过来
			// ping包后server会返回连接关闭标志并关闭连接 =>出于资源消耗考虑选false
			PermitWithoutStream: false,
		},
	},
	TaskListenPort:         10000,
	WorkerNumber:           60,
	WorkerChanTimeOut:      10,
	DbaBosBucket:           "dxm-dbbk",
	DbaBosAccessKeyID:      "dacddc31b95c441ca979bdae38f775bb",
	DbaBosAccessKeySecret:  "c91dc9682248442b8b336ab98c285b6a",
	DbaBosEndPoint:         "hb-fsg.bcebos.com",
	SiodBosBucket:          "dxm-siod-backup-hn",
	SiodBosAccessKeyID:     "407f96116a214e1e9e610efb8ca62e80",
	SiodBosAccessKeySecret: "cb782b73064f49ed8b2ec00a75d579e7",
	SiodBosEndPoint:        "gzfsg.bcebos.com",
	BosRetryCount:          5,
	MysqlNormalPackage:     "wget ftp://dba-ftp.duxiaoman-int.com:80/install_cluster_soft/matrix/mysql/general/mysql.tar.gz",
	MysqlFdbPackage:        "wget ftp://dba-ftp.duxiaoman-int.com:80/install_cluster_soft/matrix/mysql/fdb/mysql.tar.gz",
}

type Dsn struct {
	Addr    string `yaml:"addr"`
	Port    int    `yaml:"port"`
	Schema  string `yaml:"schema"`
	User    string `yaml:"user"`
	Passwd  string `yaml:"passwd"`
	Basedir string `yaml:"basedir"`
	Charset string `yaml:"charset"`
	Disable bool   `yaml:"disable"`
}

//keep alive配置
type keepaliveclientparam struct {
	Time                int  `yaml:"time"`
	TimeOut             int  `yaml:"time-out"`
	PermitWithOutStream bool `yaml:"permit-without-stream"`
}
type keepaliveserverparam struct {
	MaxConnectionIdle     int `yaml:"max-connection-idle"`
	MaxConnectionAge      int `yaml:"max-connection-age"`
	MaxConnectionAgeGrace int `yaml:"max-connection-age-grace"`
	Time                  int `yaml:"time"`
	Timeout               int `yaml:"time-out"`
}
type keepaliveenforcementpolicy struct {
	MinimumTime         int  `yaml:"minimum-time"`
	PermitWithoutStream bool `yaml:"permit-without-stream"`
}
type rpckeepalive struct {
	RpcClientParam    *keepaliveclientparam       `yaml:"client-param"`
	RpcServerParam    *keepaliveserverparam       `yaml:"server-param"`
	EnforcementPolicy *keepaliveenforcementpolicy `yaml:"enforcement-policy"`
}

type Rpcdsn struct {
	Addr              string `yaml:"addr"`
	Port              int    `yaml:"port"`
	ConnectionTimeout int    `yaml:"connection-timeout"`
}

// 加载配置文件
func (conf *Configuration) readConfigFile(path string) error {
	configFile, err := os.Open(path)
	if err != nil {
		os.Stderr.WriteString(fmt.Sprintf("readConfigFile(%s) os.Open failed: %v", path, err))
		return err
	}
	defer configFile.Close()
	content, err := ioutil.ReadAll(configFile)
	if err != nil {
		os.Stderr.WriteString(fmt.Sprintf("readConfigFile(%s) ioutil.ReadAll failed: %v", path, err))
		return err
	}
	err = yaml.Unmarshal(content, Config)
	if err != nil {
		os.Stderr.WriteString(fmt.Sprintf("readConfigFile(%s) yaml.Unmarshal failed: %v", path, err))
		return err
	}
	return nil
}

// 配置初始化
func ParseConfig(configFile string) error {
	var err error
	// 如果未传入配置文件，则返回报错
	if "" == configFile {
		return errors.New("No config file input")
	}
	// 配置文件状态检查
	if _, err = os.Stat(configFile); err != nil {
		os.Stderr.WriteString(fmt.Sprintf("%v [mdc-agent start failed] Check config file failed."+
			" err=%v ConfFile=%v \n", time.Now().Format(TIME_FORMAT), err, configFile))
		return err
	}
	// 配置文件解析
	if err = Config.readConfigFile(configFile); err != nil {
		os.Stderr.WriteString(fmt.Sprintf("%v [mdc-agent start failed]"+
			" Parse config file failed. ConfFile=%v err=%v \n", time.Now().Format(TIME_FORMAT), err, configFile))
		return err
	}
	// 初始化boscmd配置
	if err = InitBosCmdConfig(); err != nil {
		os.Stderr.WriteString(fmt.Sprintf("%v [mdc-agent start failed]"+
			" InitBosCmdConfig file failed. ConfFile=[%v] err=[%v] \n", time.Now().Format(TIME_FORMAT), err, configFile))
		return err
	}

	return LoggerInit()
}

// 分区恢复的的对象配置
type RestorePartitionToBos struct {
	// 打包的数据库列表
	DbList []string
	// 基础的var目录
	BaseVarPath string
	// 新包存在的上层目录
	FilePath string
	// 新包的目录名称
	DirName string
	// 上传至bos的路径
	BosPath string
}

// 初始化boscmd配置
func InitBosCmdConfig() error {
	// 复制模板文件夹
	output, status, err := SyncExecShell(fmt.Sprintf("cd conf && mkdir -p .%v &&  cp -r dbbk-templete/* .%v/",
		Config.DbaBosBucket, Config.DbaBosBucket))
	if err != nil {
		Log.Error("can't init boscmd config dir. output=[%v] err=[%v] status=[%v]", output, err, status)
		return err
	}
	// 修改模板文件夹内容
	sedShell := fmt.Sprintf("cd conf/.%v && sed -i 's/{{.Bucket}}/%v/g;s/{{.Endpoint}}/%v/g;s/{{.Ak}}/%v/g;s/{{.Sk}}/%v/g;s/{{.TimeStamp}}/%v/g' bucket_endpoint_cache config credentials",
		Config.DbaBosBucket, Config.DbaBosBucket, Config.DbaBosEndPoint, Config.DbaBosAccessKeyID, Config.DbaBosAccessKeySecret, time.Now().Unix())
	output, status, err = SyncExecShell(sedShell)
	if err != nil || status != 0 {
		Log.Error("can't sed config boscmd config dir. output=[%v] err=[%v] status=[%v]", output, err, status)
		return err
	}
	return nil
}
