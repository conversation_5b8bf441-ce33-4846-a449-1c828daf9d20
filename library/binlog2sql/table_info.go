package binlog2sql

import (
	"fmt"
	"strings"

	"database/sql"

	"mdc-agent/common"
)

var tableMap map[string]*TableInfo //表结构信息缓存，有表则不用去反查数据库 以db.table保存

const (
	SHOW_IDNEX_LEN  = 13
	SHOW_COLUMN_LEN = 6
)

// ColumnInfo 列信息，字段名称，字段类型
type ColumnInfo struct {
	columnType string
	columnName string
}

//Getter函数，用来外面的包获得表信息（写单测用）
func (col ColumnInfo) ColumnType() string {
	return col.columnType
}

//Getter函数，用来外面的包获得表信息（写单测用）
func (col ColumnInfo) ColumnName() string {
	return col.columnName
}

// TableInfo 表结构信息，包括表名，库名，字段信息，主键
type TableInfo struct {
	database      string
	table         string       //test.t
	columns       []ColumnInfo ///ID int\n Name varchar(20)
	primaryKey    []string     //(ID,ID2) //主键
	uniqueKeys    [][]string   //唯一主键/索引
	primarykeyMap []int        //主键在所有列中的位置
}

//Getter函数，用来外面的包获得表信息（写单测用）
func (tableInfo *TableInfo) Database() string {
	return tableInfo.database
}

//Getter函数，用来外面的包获得表信息（写单测用）
func (tableInfo *TableInfo) Table() string {
	return tableInfo.table
}

//Getter函数，用来外面的包获得表信息（写单测用）
func (tableInfo *TableInfo) Columns() []ColumnInfo {
	return tableInfo.columns
}

//Getter函数，用来外面的包获得表信息（写单测用）
func (tableInfo *TableInfo) PrimaryKey() []string {
	return tableInfo.primaryKey
}

//Getter函数，用来外面的包获得表信息（写单测用）
func (tableInfo *TableInfo) UniqueKeys() [][]string {
	return tableInfo.uniqueKeys
}

//Getter函数，用来外面的包获得表信息（写单测用）
func (tableInfo *TableInfo) PrimarykeyMap() []int {
	return tableInfo.primarykeyMap
}

//反查数据库找到字段信息
func (table *TableInfo) getColumnsFromDB(database *sql.DB) error {
	var (
		columnName string
		columnType string
	)
	var dbTbColumns []ColumnInfo
	//构造反查表语句，从数据库获得表的字段信息
	query := fmt.Sprintf("SHOW COLUMNS FROM `%s`.`%s`", table.database, table.table)
	rows, err := database.Query(query)
	if err != nil || rows == nil {
		common.Log.Warning("ErrorMsg=[query table error] err=[%v]", err)
		return fmt.Errorf("query `%s`.`%s` error , maybe dropped", table.database, table.table)
	}
	defer rows.Close()
	rowColumns, err := rows.Columns()
	if err != nil || rowColumns == nil {
		common.Log.Warning("ErrorMsg=[get rows columns error] err=[%v]", err)
		return err
	}
	for rows.Next() {
		data := make([]sql.RawBytes, len(rowColumns))
		values := make([]interface{}, len(rowColumns))
		for i := range values {
			values[i] = &data[i]
		}
		err = rows.Scan(values...) //将query的结果转换成byte类型
		if err != nil {
			common.Log.Warning("ErrorMsg=[rows scan err] err=[%v]", err)
			return err
		}
		//第一列是表名，第二列是字段类型
		//ID	int(11)
		//val	varchar(12)
		if len(data) != SHOW_COLUMN_LEN {
			common.Log.Warning("ErrorMsg=[get null data]")
			return fmt.Errorf("get null data")
		}
		columnName = string(data[0])
		if string(data[1]) == SQL_TYPE_TEXT {
			//所有的text类型以blob类型记录统一
			columnType = SQL_TYPE_BLOB
		} else {
			columnType = string(data[1])
		}
		dbTbColumns = append(dbTbColumns, ColumnInfo{columnName: columnName, columnType: columnType})
	}
	table.columns = dbTbColumns
	return nil
}

//根据主键名称获得主键列序号
func (table *TableInfo) getPrimeryKeymap() {
	var add int = 0
	table.primarykeyMap = make([]int, len(table.primaryKey))
	for index, primaryKey := range table.primaryKey {
		for primaryKey != table.columns[add].columnName {
			add++
		}
		table.primarykeyMap[index] = add

	}
}

//反查数据库获得表的主键位置和唯一索引位置
func (table *TableInfo) getKeysFromDB(database *sql.DB) error {
	var (
		primaryKeys []string
		uniqueKeys  [][]string
		rowNumber   int32 = 0
	)
	keys := make(map[string][]string, 0)
	query := fmt.Sprintf("SHOW INDEX FROM `%s`.`%s`", table.database, table.table)
	rows, err := database.Query(query)
	if err != nil || rows == nil {
		common.Log.Warning("ErrorMsg=[failed to query mysql] query=[%v] err=[%v]", query, err)
		return err
	}
	defer rows.Close()

	rowColumns, err := rows.Columns()
	if err != nil || rowColumns == nil {
		common.Log.Warning("ErrorMsg=[get columns name err] err=[%v]", err)
		return err
	}

	for rows.Next() {
		data := make([]sql.RawBytes, len(rowColumns))
		values := make([]interface{}, len(rowColumns))
		for i := range values {
			values[i] = &data[i]
		}

		err = rows.Scan(values...)
		if err != nil {
			common.Log.Warning("ErrorMsg=[rows scan err.] err=[%v]", err)
			return err
		}
		if len(data) != SHOW_IDNEX_LEN {
			common.Log.Warning("ErrorMsg=[get null data]")
			return fmt.Errorf("get null data")
		}
		nonUnique := string(data[1])
		if nonUnique == "0" {
			kName := string(data[2])
			//已经有对应的的索引组合则加入该列，否则新建
			if _, ok := keys[kName]; !ok {
				keys[kName] = make([]string, 0)
			}
			colName := string(data[4])
			if !containsString(keys[kName], colName) {
				keys[kName] = append(keys[kName], colName)
			}
			//记录主键
			if strings.Contains(strings.ToLower(kName), "primary") {
				primaryKeys = append(primaryKeys, colName)
			}
		}
		for _, uniqueKey := range keys {
			uniqueKeys = append(uniqueKeys, uniqueKey)
		}
		rowNumber++

	}
	table.uniqueKeys = uniqueKeys
	table.primaryKey = primaryKeys
	return nil

}

//反查数据库获得所有表结构信息
func (table *TableInfo) getInfoFromDB(database *sql.DB, databaseName string, tableName string) error {
	var err error
	table.database = databaseName
	table.table = tableName
	err = table.getColumnsFromDB(database)
	if err != nil {
		common.Log.Warning("ErrorMsg=[get columns from db error] err=[%v]", err)
		return err
	}
	err = table.getKeysFromDB(database)
	if err != nil {
		common.Log.Warning("ErrorMsg=[get keys fron db error] err=[%v]", err)
		return err
	}
	table.getPrimeryKeymap()
	return nil
}

// GetTbInfo 根据表名获得表信息，先从map中查询，没有则去查数据库
func GetTbInfo(ctx *Configs, databaseName string, tableName string) (*TableInfo, error) {
	var (
		tableID string
		ok      bool
		result  *TableInfo
		err     error
	)
	tableID = fmt.Sprintf("%v.%v", databaseName, tableName)
	result, ok = tableMap[tableID]
	if !ok {
		result = new(TableInfo)
		err = result.getInfoFromDB(ctx.db, databaseName, tableName)
		if err != nil {
			common.Log.Warning("ErrorMsg=[get info from db error] err=[%v]", err)
			return nil, err
		}
		if tableMap == nil {
			tableMap = make(map[string]*TableInfo, 0)
		}
		tableMap[tableID] = result
	}
	return result, err
}
