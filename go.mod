module mdc-agent

go 1.23.4

replace (
	dt-common => gitlab.duxiaoman-int.com/dba/dt-common v0.0.0-20231223100024-eecaa3dd0578
	dxm/noah-sdk/noah_golang_sdk v0.0.0-00010101000000-000000000000 => gitlab.duxiaoman-int.com/noah-sdk/noah_golang_sdk v0.0.4
)

require (
	dt-common v0.0.0-00010101000000-000000000000
	dxm/noah-sdk/noah_golang_sdk v0.0.0-00010101000000-000000000000
	github.com/astaxie/beego v1.12.3
	github.com/baidubce/bce-sdk-go v0.9.242
	github.com/dropbox/godropbox v0.0.0-20230623171840-436d2007a9fd
	github.com/go-mysql-org/go-mysql v1.13.0
	github.com/go-sql-driver/mysql v1.9.3
	github.com/gopherjs/gopherjs v1.17.2 // indirect
	github.com/smartystreets/goconvey v1.8.1
	google.golang.org/grpc v1.75.0
	gopkg.in/ini.v1 v1.67.0
	gopkg.in/yaml.v2 v2.4.0
)

require (
	github.com/natefinch/lumberjack v2.0.0+incompatible
	go.uber.org/zap v1.27.0
)

require (
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/bitly/go-simplejson v0.5.1-0.20181114203107-9db4a59bd4d8 // indirect
	github.com/goccy/go-json v0.10.2 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/klauspost/compress v1.17.8 // indirect
	github.com/pingcap/errors v0.11.5-0.20250318082626-8f80e5cb09ec // indirect
	github.com/pingcap/log v1.1.1-0.20241212030209-7e3ff8601a2a // indirect
	github.com/pingcap/tidb/pkg/parser v0.0.0-20250421232622-526b2c79173d // indirect
	github.com/shiena/ansicolor v0.0.0-20151119151921-a422bbe96644 // indirect
	github.com/shopspring/decimal v1.2.0 // indirect
	github.com/smarty/assertions v1.15.0 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/net v0.41.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/text v0.26.0 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250707201910-8d1bb00bc6a7 // indirect
	google.golang.org/protobuf v1.36.6 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.2.1 // indirect
)
