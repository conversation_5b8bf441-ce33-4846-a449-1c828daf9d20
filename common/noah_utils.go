package common

import (
	"fmt"
	"strings"

	"dxm/noah-sdk/noah_golang_sdk/src/libapptree"
)

const (
	TOKEN_PREFIX = "Bearer"
	NOAH_TIMEOUT = 1000
)

func GetNoahToken() string {
	if !strings.HasPrefix(Config.NoahToken, TOKEN_PREFIX) {
		return fmt.Sprintf("%s %s", TOKEN_PREFIX, Config.NoahToken)
	}

	return Config.NoahToken
}

func GetInstanceByCloudApp(bns string) ([]string, error) {
	// 获取 apptree 对象
	apptree, err := libapptree.ApptreeNew(nil, nil)
	if err != nil {
		Log.Warn("failed to create noah apptree, err=[%v]", err)
		return nil, err
	}
	// 获取实例列表
	instanceList, err := apptree.GetInstanceByService(bns, GetNoahToken(), NOAH_TIMEOUT)
	if err != nil {
		Log.Warn("failed to get instance by cloudapp, err=[%v]", err)
		return nil, err
	}
	ipList := make([]string, 0)
	for _, instance := range instanceList {
		ipList = append(ipList, instance.IpStr)
	}

	return ipList, nil
}
