package binlog2sql

import (
	"strings"

	SQL "github.com/dropbox/godropbox/database/sqlbuilder"
	"github.com/go-mysql-org/go-mysql/mysql"
	"github.com/go-mysql-org/go-mysql/replication"
)

const (
	STRING_TYPE_META_MAX = 256
	STRING_META_LEN_MASK = 0x30
)

//使用主键构建where条件
func genEqualConditions(row []interface{}, colDefs []SQL.NonAliasColumn, primaryKey []int) []SQL.BoolExpression {
	//如果主键为空，则使用所有列来构造where语句，否则使用主键来构建
	if len(primaryKey) == 0 {
		expArrs := make([]SQL.BoolExpression, len(row))
		for index, val := range row {
			expArrs[index] = SQL.EqL(colDefs[index], val)
		}
		return expArrs
	}
	expArrs := make([]SQL.BoolExpression, len(primaryKey))
	for index, primaryKeyAddress := range primaryKey {
		expArrs[index] = SQL.EqL(colDefs[index], row[primaryKeyAddress])
	}
	return expArrs
}

//生成set部分
func genUpdateSetPart(
	updateSQL SQL.UpdateStatement,
	rowBefore []interface{},
	rowAfter []interface{},
	colDefs []SQL.NonAliasColumn,
	colTypeNames []string) SQL.UpdateStatement {
	for index, val := range rowAfter {
		ifUpdate := false
		if containsString(
			//对于blob，json等类型，数据为slice进行slice比较
			[]string{SQL_TYPE_BLOB, SQL_TYPE_JSON, SQL_TYPE_GEOMETRY, SQL_TYPE_UNKNOW_TYPE},
			colTypeNames[index]) && !strings.Contains(strings.ToLower(colTypeNames[index]), SQL_TYPE_TEXT) {
			afterArr, afterOk := val.([]byte)
			beforeArr, beforeOk := rowBefore[index].([]byte)
			if afterOk && beforeOk {
				if !compareSlice(afterArr, beforeArr) {
					ifUpdate = true
				}
			} else {
				ifUpdate = true
			}
		} else {
			//对于其他类型，直接比较是否相等
			if val != rowBefore[index] {
				ifUpdate = true
			}
		}
		if ifUpdate {
			//如果前后数据不同则加入到set语句中
			updateSQL.Set(colDefs[index], SQL.Literal(val))
		}
	}
	return updateSQL
}

//insert语句中的values部分，将数据转换成sqlbuilder中的结构体，方便用来生成insert语句
func convertRowToExperssionRow(row []interface{}) []SQL.Expression {
	valueInserted := []SQL.Expression{}
	for _, val := range row {
		//根据原始数据生成一个转义字符串
		valExp := SQL.Literal(val)
		valueInserted = append(valueInserted, valExp)
	}
	return valueInserted
}

//根据列信息初始化sqlbuilder包中的列信息结构体和字段类型
func getSQLColumnExpressions(colCnt int, colNames []ColumnInfo, tableMap *replication.TableMapEvent) ([]SQL.NonAliasColumn, []string) {
	colDefExps := make([]SQL.NonAliasColumn, colCnt)
	colTypeNames := make([]string, colCnt)
	for i := 0; i < colCnt; i++ {
		//根据某列的信息和binlogEvent中记录的类型获得字段类型和结构体
		typeName, colDef := getMysqlTypeNameAndSQLColumn(colNames[i].columnType, colNames[i].columnName, tableMap.ColumnType[i], tableMap.ColumnMeta[i])
		colDefExps[i] = colDef
		colTypeNames[i] = typeName
	}
	return colDefExps, colTypeNames
}

//通过比较binlog中记录的表的字段类型和查询数据库获得的数据类型来判断是否发生了表结构变更
//（只能判断字段类型有无发生改变，如果字段名称发生改变则检测不出来）
func checkDDLChanges(colTypeNamesFromSQL []ColumnInfo, colTypeNamesFromRowEvent []string) bool {
	if len(colTypeNamesFromSQL) != len(colTypeNamesFromRowEvent) {
		return true
	}
	for index, types := range colTypeNamesFromSQL {
		if !strings.Contains(types.ColumnType(), colTypeNamesFromRowEvent[index]) {
			return true
		}
	}
	return false
}

//根据binlog中定义的byte类型的type序号，返回某一列对应的字段类型和sqlbuilder中记录字段信息的结构体
func getMysqlTypeNameAndSQLColumn(typeDefine string, colName string, columnType byte, meta uint16) (string, SQL.NonAliasColumn) {
	// for unknown type, defaults to BytesColumn
	//get real string type
	//string类型，根据协议有meta信息，处理meta信息
	//协议内容:The first byte is always MYSQL_TYPE_VAR_STRING (i.e., 253). The second byte is the field size, i.e.
	//the number of bytes in the representation of size of the string: 3 or 4
	//此处根据meta额外信息获得具体类型
	if columnType == mysql.MYSQL_TYPE_STRING {
		if meta >= STRING_TYPE_META_MAX {
			high := uint8(meta >> 8)
			if high&STRING_META_LEN_MASK != STRING_META_LEN_MASK {
				columnType = byte(high | STRING_META_LEN_MASK)
			} else {
				columnType = high
			}
		}
	}
	switch columnType {
	case mysql.MYSQL_TYPE_NULL:
		return SQL_TYPE_UNKNOW_TYPE, SQL.BytesColumn(colName, SQL.NotNullable)
	case mysql.MYSQL_TYPE_LONG:
		return SQL_TYPE_INT, SQL.IntColumn(colName, SQL.NotNullable)
	case mysql.MYSQL_TYPE_TINY:
		return SQL_TYPE_TINY_INT, SQL.IntColumn(colName, SQL.NotNullable)
	case mysql.MYSQL_TYPE_SHORT:
		return SQL_TYPE_SMALL_INT, SQL.IntColumn(colName, SQL.NotNullable)
	case mysql.MYSQL_TYPE_INT24:
		return SQL_TYPE_MEDIUM_INT, SQL.IntColumn(colName, SQL.NotNullable)
	case mysql.MYSQL_TYPE_LONGLONG:
		return SQL_TYPE_BIG_INT, SQL.IntColumn(colName, SQL.NotNullable)
	case mysql.MYSQL_TYPE_NEWDECIMAL:
		return SQL_TYPE_DECIMAL, SQL.DoubleColumn(colName, SQL.NotNullable)
	case mysql.MYSQL_TYPE_FLOAT:
		return SQL_TYPE_FLOAT, SQL.DoubleColumn(colName, SQL.NotNullable)
	case mysql.MYSQL_TYPE_DOUBLE:
		return SQL_TYPE_DOUBLE, SQL.DoubleColumn(colName, SQL.NotNullable)
	case mysql.MYSQL_TYPE_BIT:
		return SQL_TYPE_BIT, SQL.IntColumn(colName, SQL.NotNullable)
	case mysql.MYSQL_TYPE_TIMESTAMP:
		return SQL_TYPE_DATETIME, SQL.StrColumn(colName, SQL.UTF8, SQL.UTF8CaseInsensitive, SQL.NotNullable)
	case mysql.MYSQL_TYPE_TIMESTAMP2:
		return SQL_TYPE_DATETIME, SQL.StrColumn(colName, SQL.UTF8, SQL.UTF8CaseInsensitive, SQL.NotNullable)
	case mysql.MYSQL_TYPE_DATETIME:
		return SQL_TYPE_DATETIME, SQL.StrColumn(colName, SQL.UTF8, SQL.UTF8CaseInsensitive, SQL.NotNullable)
	case mysql.MYSQL_TYPE_DATETIME2:
		return SQL_TYPE_DATETIME, SQL.StrColumn(colName, SQL.UTF8, SQL.UTF8CaseInsensitive, SQL.NotNullable)
	case mysql.MYSQL_TYPE_TIME:
		return SQL_TYPE_TIME, SQL.StrColumn(colName, SQL.UTF8, SQL.UTF8CaseInsensitive, SQL.NotNullable)
	case mysql.MYSQL_TYPE_TIME2:
		return SQL_TYPE_TIME, SQL.StrColumn(colName, SQL.UTF8, SQL.UTF8CaseInsensitive, SQL.NotNullable)
	case mysql.MYSQL_TYPE_DATE:
		return SQL_TYPE_DATE, SQL.StrColumn(colName, SQL.UTF8, SQL.UTF8CaseInsensitive, SQL.NotNullable)
	case mysql.MYSQL_TYPE_YEAR:
		return SQL_TYPE_YEAR, SQL.IntColumn(colName, SQL.NotNullable)
	case mysql.MYSQL_TYPE_ENUM:
		return SQL_TYPE_ENUM, SQL.IntColumn(colName, SQL.NotNullable)
	case mysql.MYSQL_TYPE_SET:
		return SQL_TYPE_SET, SQL.IntColumn(colName, SQL.NotNullable)
	case mysql.MYSQL_TYPE_BLOB:
		//将text类型统一记录成blob类型处理
		if strings.Contains(strings.ToLower(typeDefine), SQL_TYPE_TEXT) {
			return SQL_TYPE_BLOB, SQL.StrColumn(colName, SQL.UTF8, SQL.UTF8CaseInsensitive, SQL.NotNullable)
		}
		return SQL_TYPE_BLOB, SQL.BytesColumn(colName, SQL.NotNullable)
	case mysql.MYSQL_TYPE_VARCHAR,
		mysql.MYSQL_TYPE_VAR_STRING:
		return SQL_TYPE_VARCHAR, SQL.StrColumn(colName, SQL.UTF8, SQL.UTF8CaseInsensitive, SQL.NotNullable)
	case mysql.MYSQL_TYPE_STRING:
		return SQL_TYPE_CHAR, SQL.StrColumn(colName, SQL.UTF8, SQL.UTF8CaseInsensitive, SQL.NotNullable)
	case mysql.MYSQL_TYPE_JSON:
		return SQL_TYPE_JSON, SQL.StrColumn(colName, SQL.UTF8, SQL.UTF8CaseInsensitive, SQL.NotNullable)
	case mysql.MYSQL_TYPE_GEOMETRY:
		return SQL_TYPE_GEOMETRY, SQL.BytesColumn(colName, SQL.NotNullable)
	default:
		return SQL_TYPE_UNKNOW_TYPE, SQL.BytesColumn(colName, SQL.NotNullable)
	}
}
