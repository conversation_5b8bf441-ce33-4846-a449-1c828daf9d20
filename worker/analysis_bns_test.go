package worker

import (
	"fmt"
	"sync"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"

	"mdc-agent/common"
)

func init() {
	common.MdcServerIpList.RwLock = new(sync.RWMutex)
}
func TestUpdateIpList(t *testing.T) {
	common.Config.MdcRpcServer = &common.Rpcdsn{
		Addr:              "cluster-test.siod-mysql",
		Port:              8888,
		ConnectionTimeout: 30,
	}
	err := UpdateIpList()
	Convey("UpdateIpList 成功", t, func() {
		So(err, ShouldBeNil)
	})
}

func TestGetInstanceListByService(t *testing.T) {
	ipList, err := GetInstanceListByService("cluster-test.siod-mysql")
	Convey("GetInstanceListByService 成功", t, func() {
		So(err, ShouldBeNil)
		fmt.Printf("ipList=[%v]\n", ipList)
	})

	//测试不存在的bns
	ipList, err = GetInstanceListByService("aa.bb")

	<PERSON>vey("GetInstanceListByService 测试不存在的bns成功", t, func() {
		So(err, ShouldNotBeNil)
	})
}

func TestSyncExecShell(t *testing.T) {

	cmdStr := fmt.Sprintf("timeout 2 get_instance_by_cloudapp -i cluster-test.siod-mysql")
	resStr, _, reloadErr := common.SyncExecShell(cmdStr)
	Convey("Execute linux shell command successed", t, func() {
		So(reloadErr, ShouldBeNil)
		fmt.Printf("cmdStr=[%v]\n", resStr)
	})

	cmdStr = fmt.Sprintf("timeout 2 get_instance_by_cloudapp -i aa.bb")
	resStr, _, reloadErr = common.SyncExecShell(cmdStr)
	Convey("Execute linux shell command failed", t, func() {
		So(reloadErr, ShouldNotBeNil)
		fmt.Printf("cmdStr=[%v]\n", resStr)
	})
}

func TestStartAnalysisBns(t *testing.T) {
	Convey("启动&关闭 AnalysisBns", t, func() {
		common.Config.BnsReloadCycle = 10
		bnsCollectorTimerController = new(bnsCollectorTimer)
		//主协程等待变量
		var waitgroup sync.WaitGroup
		waitgroup.Add(1)
		go StartAnalysisBns(&waitgroup)
		time.Sleep(20 * time.Second)
		StopAnalysisBns()
		waitgroup.Done()
		waitgroup.Wait()

		So(common.MdcServerIpList.IpList, ShouldNotBeEmpty)
		fmt.Printf("ipList=[%v]\n", common.MdcServerIpList.IpList)
	})
}

func TestStringSliceEqual(t *testing.T) {
	a := []string{"*******", "*******"}
	b := []string{"*******", "*******"}
	c := []string{"*******"}
	d := []string{"*******", "*******", "*******"}
	Convey("StringSliceEqual 相同Slice测试", t, func() {

		res := StringSliceEqual(a, a)
		So(res, ShouldBeTrue)
	})
	Convey("StringSliceEqual 不同Slice测试", t, func() {

		res1 := StringSliceEqual(a, b)
		res2 := StringSliceEqual(a, c)
		res3 := StringSliceEqual(a, d)
		So(res1, ShouldBeFalse)
		So(res2, ShouldBeFalse)
		So(res3, ShouldBeFalse)
	})
}
