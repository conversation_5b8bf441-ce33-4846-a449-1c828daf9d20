package binlogtosql

import (
	"database/sql"
	"errors"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"mdc-agent/common"

	"github.com/go-mysql-org/go-mysql/replication"
)

type Configs struct {
	//数据库信息
	Host   string
	Port   string
	User   string
	Passwd string

	//Binlog文件
	FileList  []string
	StartTime string
	EndTime   string

	startTime uint32
	endTime   uint32

	EventChan  chan *replication.BinlogEvent //事件队列，用来传输需要解析为SQL的binloogEvent
	StatusChan chan bool                     //当文件解析结束或出现错误时向主程序发送终止信号

	stopped bool //当解析sql出错时向parse协程发送停止信号
	//要连接的备库
	db *sql.DB
}

const (
	LOCAL_LOCATION  = "Local"
	EVENT_CHAN_SIZE = 100 //队列缓冲区大小默认100
)

var binlogToSQLCfg *Configs = &Configs{}

//ParseFileParams
/**
*Description:
*	解析binlog文件
*	使用方法binlogTosql.ParseFile("localhost","3306","root","pwd","2020-00-01 15:00:00","2021-00-02 15:00:00")
*Params:
*	Host&Port:被解析数据库的IP和端口号
*	User&Passwd:用于登陆数据库的用户名和密码
*	FileList:拉取到的binlog文件的绝对路径
*	StartTime:解析事件的起始时间
*	EndTime:解析事件的终止时间*/
type ParseFileParams struct {
	Host      string
	Port      string
	User      string
	Passwd    string
	FileList  []string
	StartTime string
	EndTime   string
}

//ParseFile
/**
*Description:
*	解析binlog文件
*	使用方法binlogTosql.ParseFile("localhost","3306","root","pwd","2020-00-01 15:00:00","2021-00-02 15:00:00")
*Params:
*	Host&Port:被解析数据库的IP和端口号
*	User&Passwd:用于登陆数据库的用户名和密码
*	FileList:拉取到的binlog文件的绝对路径
*	StartTime:解析事件的起始时间
*	EndTime:解析事件的终止时间
*Returns:
*	[]string:解析成功的SQL语句
*	error:
*		解析失败返回error
*		失败情况:
*		解析到的table在数据库中不存在	返回errors.New("%s  does not exist .")
*		解析到的table表结构发生改变（删除/增加字段/改变字段类型） 返回errors.New("Some DDL happens")
*		(如果表结构未改变的DDL则不会被监测到如：更改字段名/删除一列同时增加一列类型相同的字段)
 */
func ParseFile(p ParseFileParams, taskId int64) (string, error) {
	var (
		parseFileName string
		tarFilePath string
		err  error
		wg   sync.WaitGroup
	)

	binlogToSQLCfg.FileList = p.FileList
	binlogToSQLCfg.Host = p.Host
	binlogToSQLCfg.Port = p.Port
	binlogToSQLCfg.User = p.User
	binlogToSQLCfg.Passwd = p.Passwd
	binlogToSQLCfg.StartTime = p.StartTime
	binlogToSQLCfg.EndTime = p.EndTime
	err = binlogToSQLCfg.SetTime()
	if err != nil {
		common.Log.Critical("ErrorMsg = [set time error.] err= [%v]", err)
		return "", err
	}
	err = binlogToSQLCfg.OpenChansAndDb()
	if err != nil {
		common.Log.Warning("ErrorMsg = [open channle or db error] err= [%v]", err)
		return "", err
	}
	defer binlogToSQLCfg.Close()

	// 获取解析SQL目标路径
	pathList := strings.Split(p.FileList[0], "/")
	if len(pathList) == 0 {
		errMsg := fmt.Sprintf("get parase file path failed, pathList is nil")
		common.Log.Error(errMsg)
		return "", errors.New(errMsg)
	}

	for idx, dirName := range pathList[:len(pathList)-1] {
		if dirName == "" {
			errMsg := fmt.Sprintf("format parase binlog dir name is nil")
			common.Log.Error(errMsg)
			return "", errors.New(errMsg)
		}
		if idx == 0 {
			tarFilePath = fmt.Sprintf("%v", dirName)
		} else {
			tarFilePath = fmt.Sprintf(tarFilePath+"/%v", dirName)
		}
	}

	// 生成解析SQL文件名
	parseFileName = fmt.Sprintf("%v/%v.sql", tarFilePath, taskId)

	wg.Add(1)
	//开启解析文件协程，解析过滤binlog文件，发送到eventchannel
	go ParseEventsFromFile(binlogToSQLCfg, &wg)
	//循环读取channel，将event解析为SQL
	err = GenericSQLs(binlogToSQLCfg, parseFileName)
	if err != nil {
		common.Log.Warning("ErrorMsg=[generic SQL error] err=[%v]", err)
		//如果提前报错终止，则设置停止位，等待Parse协程终止
		binlogToSQLCfg.stopped = true
		for {
			select {
			case <-binlogToSQLCfg.EventChan: //Parse协程有未处理的event时，从channel队列拿出event防止Parse协程阻塞
				break
			case <-binlogToSQLCfg.StatusChan: //等待Parse协程处理完成
				return parseFileName, err
			}
		}
	}
	wg.Wait()
	return parseFileName, err
}

//Close()
//函数结束完成关闭所有打开的channel和数据库
func (ctx *Configs) Close() {
	if ctx.db != nil {
		ctx.db.Close()
		ctx.db = nil
	}
	if ctx.EventChan != nil {
		close(ctx.EventChan)

	}
	if ctx.StatusChan != nil {
		close(ctx.StatusChan)
	}
	tableMap = nil
	ctx.stopped = false
}

// SetTime
//时间转换和判断函数，用来将字符串类型的起止时间转换为时间戳，失败返回错误
func (ctx *Configs) SetTime() error {
	var (
		timeLocation *time.Location
		err          error
	)
	timeLocation, err = time.LoadLocation(LOCAL_LOCATION)

	if err != nil {
		common.Log.Critical("ErrorMsg=[get time location error] err=[%v]", err)
		return err
	}
	timestamp, err := time.ParseInLocation(DATETIME_FORMAT, ctx.StartTime, timeLocation)
	if err != nil {
		common.Log.Critical("ErrorMsg = [invalid starttime] starttime=[%s] err=[%v]", ctx.StartTime, err)
		return fmt.Errorf("set time fail due to invalid starttime starttime=[%v] err=[%v]", ctx.StartTime, err)
	}
	ctx.startTime = uint32(timestamp.Unix())
	timestamp, err = time.ParseInLocation(DATETIME_FORMAT, ctx.EndTime, timeLocation)
	if err != nil {
		common.Log.Critical("ErrorMsg=[ivalid endtime] endtime=[%v] err=[%v] ", err)
		return fmt.Errorf("set time fail due to invalid endtime endtime=[%v] err=[%v]", ctx.EndTime, err)
	}
	ctx.endTime = uint32(timestamp.Unix())
	if ctx.endTime < ctx.startTime {
		common.Log.Critical(
			"ErrorMsg=[starttime must earlier than endtime] starttime=[%v] endtime=[%v] err=[%v] ",
			ctx.StartTime, ctx.EndTime, err)
		return fmt.Errorf("starttime must earlier than endtime  starttime=[%v]  endtime=[%v]", ctx.StartTime, ctx.EndTime)
	}
	return nil
}

//OpenChansAndDb
//初始化channel和数据库，失败返回对应错误
func (ctx *Configs) OpenChansAndDb() error {
	var err error
	ctx.EventChan = make(chan *replication.BinlogEvent, EVENT_CHAN_SIZE)
	ctx.StatusChan = make(chan bool,1)
	url := fmt.Sprintf(
		"%s:%s@tcp(%s:%s)/?autocommit=true&charset=utf8mb4,utf8,latin1&loc=Local&parseTime=true",
		ctx.User, ctx.Passwd, ctx.Host, ctx.Port)
	ctx.db, err = createMysqlConnection(url)
	if err != nil {
		common.Log.Critical("ErrorMsg=[connect to mysql error] err=[%v]", err)
		return err
	}
	return nil
}

func init() {
	log.SetFlags(log.Lshortfile)
}
