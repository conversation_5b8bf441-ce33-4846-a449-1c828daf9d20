# 日志级别，默认为3:Error
# [0:Emergency, 1:<PERSON>ert, 2:Critical, 3:Error, 4:Warning, 5:Notice, 6:Informational, 7:Debug]
log-level: 7
# 日志最大保留天数
log-maxdays: 7
# 日志输出位置，默认日志输出到控制台
# 目前只支持['console', 'file']两种形式，如非console形式这里需要指定文件的路径，可以是相对路径
log-output: logs/mdc-agent.log

# 连接上线平台的配置
mdc-rpc-server:
  addr: mysql-pressure-test.siod-mysql
  port: 10000
  # http连接创建时握手交互超时时间
  connection-timeout: 20
task-listen-port: 10006
#白名单或整个配置文件自动热加载周期，单位s
bns_reload_cycle: 300
server-msgchan-bufferlength: 500
# 连接server超时时间，单位秒
server-conn-timeout: 10
# 读写server超时时间，单位秒
server-rw-timeout: 5
worker-number: 60
worker-chan-timeout: 10
dba_bos_bucket: dxm-dbbk-hb-test
dba_bos_accesskeyid: 77cbfd63fe57424db581eeac513ed86b
dba_bos_accesskeysecret: 6645ebbd54f94876ad2de7efeda9d957
dba_bos_endpoint: hb-fsg.bcebos.com
siod_bos_bucket: dxm-dbbk-hb-test
siod_bos_accesskeyid: 77cbfd63fe57424db581eeac513ed86b
siod_bos_accesskeysecret: 6645ebbd54f94876ad2de7efeda9d957
siod_bos_endpoint: hb-fsg.bcebos.com
mysql-normal-package: "wget -O mysql.tar.gz https://sqlonline.bcebos.hb-fsg.x3.dxmyun.com/mysql.tar.gz?authorization=bce-auth-v1/939f36b2d4404fb390189c5490363c2e/2023-06-12T03%3A43%3A46Z/-1/host/cdc55ccea017d0724957fcbe4838b892c715c59b9fa6ddea27101be658bcbc39"
mysql-fdb-package: "wget -O mysql.tar.gz https://sqlonline.bcebos.hb-fsg.x3.dxmyun.com/mysql_databus.tar.gz?authorization=bce-auth-v1/939f36b2d4404fb390189c5490363c2e/2023-06-12T03%3A40%3A43Z/-1/host/7373c9266648c35c4fb73aced8cf96d3aaaa7aae68a6d60e2a968b70d7495280"

