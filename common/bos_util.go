package common

import (
	"errors"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/baidubce/bce-sdk-go/services/bos/api"

	"dt-common/net_monitor/common/retry"
)

func CreatBosClient(accessKeyID, accessKeySecret, endPoint string) (*bos.Client, error) {
	// 初始化一个BosClient
	bosClient, err := bos.NewClient(accessKeyID, accessKeySecret, endPoint)
	if err != nil {
		Log.Error("Creat Bos Client failed,err =[%v]", err)
		return nil, err
	}

	//判断bucket是否存在
	exists, err := bosClient.DoesBucketExist(Config.DbaBosBucket)
	if err == nil && exists {
		return bosClient, err
	} else {
		if loc, err := bosClient.PutBucket(Config.DbaBosBucket); err != nil {
			Log.Error("create bucket failed,err=[%v]", err)
			return nil, err
		} else {
			Log.Debug("create bucket success at location,loc=[%v]", loc)
		}
	}
	return bosClient, nil
}

//从bos上下载数据到文件
func DownLoadDataFromBos(accessKeyID, accessKeySecret, endPoint, bucketName, fileBosAddr, targetFile string) error {
	Log.Debug("debug downLoadDataFromBos fileBosAddr=[%v] AccessKeyID=[%v] AccessKeySecret=[%v],tarGetfile=[%v]", fileBosAddr, accessKeyID, accessKeySecret, targetFile)
	// 初始化一个BosClient
	bosClient, err := CreatBosClient(accessKeyID, accessKeySecret, endPoint)
	if err != nil {
		Log.Warn("Fail to new client err=[%v]", err)
		return err
	}
	// 提供Bucket和Object,直接获取一个对象;改为下载过程中支持断点续传
	err = retry.Do(func() error {
		errDownLoad := bosClient.DownloadSuperFile(bucketName, fileBosAddr, targetFile)
		if errDownLoad != nil {
			Log.Warn("bos download failed and retrying. err=[%v]", errDownLoad)
		}
		return errDownLoad
	}, Config.BosRetryCount, 10*time.Second)
	if err != nil {
		Log.Error("bos retry download failed. err=[%v] retryCount=[%v]", err, Config.BosRetryCount)
		return err
	}

	return nil
}

//分块上传文件到bos
func BlockUploadToBos(filePath string, objectKey string) (bool, error) {
	var isSuccess bool

	//创建bos客户端
	bosClient, err := CreatBosClient(Config.DbaBosAccessKeyID, Config.DbaBosAccessKeySecret, Config.DbaBosEndPoint)
	if err != nil {
		errMsg := fmt.Sprintf("Create BosClient failed,err =[%v]", err)
		Log.Error(errMsg)
		return false, errors.New(errMsg)
	}
	bosClient.MaxParallel = 10
	bosClient.MultipartSize = 20 * (1 << 10)

	res, err := bosClient.BasicInitiateMultipartUpload(Config.DbaBosBucket, objectKey)
	uploadId := res.UploadId
	file, err := os.Open(filePath)
	if err != nil {
		errMsg := fmt.Sprintf("OpenFile failed,err =[%v],file=[%v]", err, filePath)
		Log.Error(errMsg)
		return false, errors.New(errMsg)
	}
	// 分块大小按MULTIPART_ALIGN=1MB对齐
	partSize := (bosClient.MultipartSize + bos.MULTIPART_ALIGN - 1) / bos.MULTIPART_ALIGN * bos.MULTIPART_ALIGN

	// 获取文件大小，并计算分块数目，最大分块数MAX_PART_NUMBER=10000
	fileInfo, err := file.Stat()
	if err != nil || fileInfo == nil {
		errMsg := fmt.Sprintf("StatFile failed,err =[%v],file=[%v]", err, filePath)
		Log.Error(errMsg)
		return false, errors.New(errMsg)
	}
	fileSize := fileInfo.Size()
	partNum := (fileSize + partSize - 1) / partSize
	if partNum > bos.MAX_PART_NUMBER { // 超过最大分块数，需调整分块大小
		partSize = (fileSize + bos.MAX_PART_NUMBER + 1) / bos.MAX_PART_NUMBER
		partSize = (partSize + bos.MULTIPART_ALIGN - 1) / bos.MULTIPART_ALIGN * bos.MULTIPART_ALIGN
		partNum = (fileSize + partSize - 1) / partSize
	}

	// 创建保存每个分块上传后的ETag和PartNumber信息的列表
	partEtags := make([]api.UploadInfoType, 0)

	// 逐个分块上传
	for i := int64(1); i <= partNum; i++ {
		// 计算偏移offset和本次上传的大小uploadSize
		uploadSize := partSize
		offset := partSize * (i - 1)
		left := fileSize - offset
		if left < partSize {
			uploadSize = left
		}

		// 创建指定偏移、指定大小的文件流
		partBody, err := bce.NewBodyFromSectionFile(file, offset, uploadSize)
		if err != nil {
			errMsg := fmt.Sprintf(" bce.NewBodyFromSectionFile failed. err=[%v] bucket=[%v] objectKey=[%v]", err, Config.DbaBosBucket, objectKey)
			Log.Error(errMsg)
			return false, err
		}
		// 上传当前分块
		etag, err := bosClient.BasicUploadPart(Config.DbaBosBucket, objectKey, uploadId, int(i), partBody)
		if err != nil {
			errMsg := fmt.Sprintf("BasicUploadPart failed,err=[%v],bucket=[%v],objectKey=[%v]", err, Config.DbaBosBucket, objectKey)
			Log.Error(errMsg)
			return false, errors.New(errMsg)
		}
		// 保存当前分块上传成功后返回的序号和ETag
		partEtags = append(partEtags, api.UploadInfoType{int(i), etag})
	}

	completeArgs := api.CompleteMultipartUploadArgs{Parts: partEtags}
	if _, err = bosClient.CompleteMultipartUploadFromStruct(Config.DbaBosBucket, objectKey, uploadId, &completeArgs); err != nil {
		isSuccess = false
		Log.Error("CompleteMultipartUploadFromStruct failed,err=[%v],bucket=[%v],objectKey=[%v]", err, Config.DbaBosBucket, objectKey)
	} else {
		isSuccess = true
	}
	return isSuccess, err
}

//上传SQL接文件到Bos
func UploadObjToBos(sqlFile string, taskId int64) (objName string, error error) {
	//上传bos
	objName = fmt.Sprintf("%v.sql", taskId)
	// 初始化一个BosClient
	bosClient, err := CreatBosClient(Config.DbaBosAccessKeyID, Config.DbaBosAccessKeySecret, Config.DbaBosEndPoint)
	if err != nil {
		Log.Warn("Fail to new client err=[%v]", err)
		return "", err
	}
	Log.Notice("UploadObjToBos,start PutObjectFromString,DbaBosBucket=[%v],objName=[%v]", Config.DbaBosBucket, objName)
	_, err = bosClient.PutObjectFromFile(Config.DbaBosBucket, objName, sqlFile, nil)
	if err != nil {
		switch realErr := err.(type) {
		case *bce.BceClientError:
			Log.Warn("Client occurs error=[%v]", realErr)
			return "", err
		case *bce.BceServiceError:
			Log.Warn("Service occurs error=[%v]", realErr)
			return "", err
		default:
			Log.Warn("Bos unknown error=[%v]", err)
			return "", err
		}
	}

	return objName, nil
}

//删除bos上的数据
func DeleteBosData(dataDir []string) (err error) {
	if len(dataDir) == 0 || dataDir == nil {
		return
	}

	// 初始化一个BosClient
	bosClient, err := bos.NewClient(Config.DbaBosAccessKeyID, Config.DbaBosAccessKeySecret, Config.DbaBosEndPoint)
	if err != nil {
		Log.Error("Fail to new  bos client,err=[%v]", err)
		return
	}
	for _, val := range dataDir {
		if val == "" {
			continue
		}

		//判断文件是否存在
		_, err := bosClient.GetObjectMeta(Config.DbaBosBucket, val)
		if realErr, ok := err.(*bce.BceServiceError); ok {
			if realErr.StatusCode == 404 {
				fmt.Println("object not exists")
				continue
			}
		}
		//删除文件
		err = bosClient.DeleteObject(Config.DbaBosBucket, val)
		if err != nil {
			Log.Error("Fail to delete bos object, err=[%v]", err)
			return err
		}
	}
	return nil
}

func DownLoadDataFromTape(dataPath, targetPath string) error {
	if dataPath == "" {
		return nil
	}

	//获得磁带库数据大小
	getTapeDataSize := fmt.Sprintf("s3cmd --config=%v du %v|awk '{print $1}'", TapeConfig, dataPath)
	size, _, err := SyncExecShell(getTapeDataSize)
	if err != nil {
		Log.Error("SyncExecShell stopXtraPid failed,err=[%v]", err)
		return err
	}
	//下载磁带库数据
	downloadTapeShell := fmt.Sprintf("nohup s3cmd --config=%v sync %v %v  --limit-rate=%v &", TapeConfig, dataPath, targetPath, TapeLimitRate)
	err = SyncExecShellNoResult(downloadTapeShell)
	if err != nil {
		Log.Error("SyncExecShell stopXtraPid failed,err=[%v]", err)
		return err
	}
	//监测是否拉取磁带库数据完成
	if err = detectDownloadTapeStatus(size, targetPath); err != nil {
		Log.Error("detectDownloadTapeStatus failed,err=[%v]", err)
		return err
	}
	return nil
}

//定时探测下载磁带库数据状态
func detectDownloadTapeStatus(size, dataPath string) error {
	tick := time.NewTicker(time.Second * time.Duration(Config.AnalysisBnsTimeout))
	Log.Notice("Start detectDownloadTapeStatus")
	for {
		select {
		case <-tick.C:
			getS3cmdShell := "ps ux | grep 's3cmd'|grep -v grep"
			result, status, err := SyncExecShell(getS3cmdShell)
			if err != nil && status != 1 {
				Log.Error("SyncExecShell SyncExecShell failed,err=[%v]", err)
				return err
			}
			if result == "" {
				fileInfo, err := os.Stat(dataPath)
				if err != nil {
					Log.Error("get file stat failed,err=[%v],dataPath=[%v]", err, dataPath)
					return err
				}
				//若文件大小相等 则说明下载
				if strconv.FormatInt(fileInfo.Size(), 10) == strings.Trim(size, " \r\n\t") {
					return nil
				} else {
					errMsg := fmt.Sprintf("filesize is not equal,fileInfo.Size=[%v],size=[%v]", fileInfo.Size(), size)
					Log.Error(errMsg)
					return errors.New(errMsg)
				}
			}
		}
	}
}

// BosCmdSyncDirToBos 上传数据到bos
func BosCmdSyncDirToBos(bucket, locaDir, bosDirPath string) error {
	Log.Info("[BosCMDSync] start sync data to bos. bucket=[%v] bosDirPath=[%v] locaDir=[%v]", bucket, bosDirPath, locaDir)
	syncShell := fmt.Sprintf("%v  --conf-path  conf/.%v bos sync %v bos:/%v/%v/ --yes ", BceCMD, bucket, locaDir, bucket, bosDirPath)
	output, status, err := SyncExecShell(syncShell)
	if status != 0 || err != nil {
		errSync := fmt.Errorf("[BosCMDSync] sync data to bos failed. bucket=[%v] bosDirPath=[%v] locaDir=[%v] output=[%v] err=[%v]",
			bucket, bosDirPath, locaDir, output, err)
		Log.Error("%v", errSync)
		return errSync
	}
	Log.Info("[BosCMDSync] sync data to bos success. bucket=[%v] bosDirPath=[%v] locaDir=[%v] output=[%v]",
		bucket, bosDirPath, locaDir, output)
	return nil
}

// BosCmdSyncDirFromBos 下载数据，支持断点续传
func BosCmdSyncDirFromBos(bucket, bosDirPath, locaDir string) error {
	Log.Info("[BosCMDSync] start sync data from bos. bucket=[%v] bosDirPath=[%v] locaDir=[%v]", bucket, bosDirPath, locaDir)
	syncShell := fmt.Sprintf("%v  --conf-path  conf/.%v bos sync  bos:/%v/%v %v --yes", BceCMD, bucket, bucket, bosDirPath, locaDir)
	output, status, err := SyncExecShell(syncShell)
	if status != 0 || err != nil {
		errSync := fmt.Errorf("[BosCMDSync] sync bos data failed. bucket=[%v] bosDirPath=[%v] locaDir=[%v] output=[%v] err=[%v]",
			bucket, bosDirPath, locaDir, output, err)
		Log.Error("%v", errSync)
		return errSync
	}
	Log.Info("[BosCMDSync] sync sync data from bos success. bucket=[%v] bosDirPath=[%v] locaDir=[%v] output=[%v]",
		bucket, bosDirPath, locaDir, output)
	return nil
}

// BosCmdListDir 列出bos目录下所有文件
func BosCmdListDir(bucket, bosDirPath string) error {
	Log.Info("[BosCMDSync] start list data from bos. bucket=[%v] bosDirPath=[%v]", bucket, bosDirPath)
	syncShell := fmt.Sprintf("%v  --conf-path  conf/.%v bos ls bos:/%v/%v/", BceCMD, bucket, bucket, bosDirPath)
	output, status, err := SyncExecShell(syncShell)
	if status != 0 || err != nil {
		errSync := fmt.Errorf("[BosCMDSync] list data from bos failed. bucket=[%v] bosDirPath=[%v] output=[%v] err=[%v]",
			bucket, bosDirPath, output, err)
		Log.Error("%v", errSync)
		return errSync
	}
	Log.Info("[BosCMDSync] list data from bos success. bucket=[%v] bosDirPath=[%v] output=[%v]",
		bucket, bosDirPath, output)
	return nil
}

// GetLocalDirFileCount 获取文件夹下文件数量
func GetLocalDirFileCount(filePath string) (int64, error) {
	output, status, err := SyncExecShell(fmt.Sprintf("ls -lR %v| grep \"^-\" | wc -l|tr -d '\\n'", filePath))
	if status != 0 || err != nil {
		err = fmt.Errorf("GetLocalDirFileCount failed. filePath=[%v] output=[%v]",
			filePath, output)
		Log.Error("%v", err)
		return -1, err
	}
	return strconv.ParseInt(output, 10, 64)
}
