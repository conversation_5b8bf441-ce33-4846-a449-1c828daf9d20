package xtrabackup

import (
	"context"
	"fmt"
	"time"
)

// ExampleUsage 展示 XtraBackup 库的使用方法
func ExampleUsage() {
	// 1. 创建配置
	config := &Config{
		BinPath:         "bin/percona-xtrabackup-2.4.15-Linux-x86_64/bin/innobackupex",
		XbstreamPath:    "bin/percona-xtrabackup-2.4.15-Linux-x86_64/bin/xbstream",
		MySQLBaseDir:    "/home/<USER>/mysql",
		MySQLUser:       "backup_user",
		MySQLPassword:   "backup_password",
		MySQLSocket:     "/home/<USER>/mysql/tmp/mysql.sock",
		ThrottleRate:    100, // 100 MB/s
		CompressLevel:   6,
		CompressThreads: 4,
		UseMemory:       "5G",
		KillLongQueryType:    "select",
		KillLongQueryTimeout: "30",
		StreamFormat:    "xbstream",
		NoTimestamp:     true,
		SlaveInfo:       true,
	}

	// 2. 创建管理器
	manager := NewManager(config)

	// 3. 设置进度回调
	manager.monitor.SetCallback(func(info *ProgressInfo) {
		fmt.Printf("Backup progress: %.2f%%, Status: %v, Message: %s\n", 
			info.Progress, info.Status, info.Message)
	})

	// 4. 执行全量备份
	ctx := context.Background()
	fullBackupOpts := &BackupOptions{
		TargetDir:    "/backup/full/20241203",
		ExtraLSNDir:  "/tmp/extra_lsn_123",
		RemoteHost:   "backup-server",
		RemotePath:   "/remote/backup/full/20241203",
		SSHKeyPath:   "bin/.id_rsa_tmp_dbbk",
		BackupOptions: "--databases=mydb",
	}

	fmt.Println("Starting full backup...")
	fullResult, err := manager.FullBackup(ctx, fullBackupOpts)
	if err != nil {
		fmt.Printf("Full backup failed: %v\n", err)
		return
	}
	fmt.Printf("Full backup completed: %+v\n", fullResult)

	// 5. 执行增量备份
	incBackupOpts := &BackupOptions{
		LSNPosition:  fullResult.LSNTo, // 使用全量备份的结束 LSN
		TargetDir:    "/backup/inc/20241204",
		ExtraLSNDir:  "/tmp/extra_lsn_124",
		RemoteHost:   "backup-server",
		RemotePath:   "/remote/backup/inc/20241204",
		SSHKeyPath:   "bin/.id_rsa_tmp_dbbk",
	}

	fmt.Println("Starting incremental backup...")
	incResult, err := manager.IncrementalBackup(ctx, incBackupOpts)
	if err != nil {
		fmt.Printf("Incremental backup failed: %v\n", err)
		return
	}
	fmt.Printf("Incremental backup completed: %+v\n", incResult)

	// 6. 恢复备份示例
	restoreOpts := &RestoreOptions{
		BackupPath:      "/backup/full/20241203",
		TargetDir:       "/restore/data",
		IncrementalDirs: []string{"/backup/inc/20241204"},
		UseMemory:       "5G",
		ParallelThreads: 16,
	}

	// 准备备份（apply-log）
	fmt.Println("Preparing backup...")
	prepareResult, err := manager.PrepareBackup(ctx, restoreOpts)
	if err != nil {
		fmt.Printf("Prepare backup failed: %v\n", err)
		return
	}
	fmt.Printf("Prepare backup completed: %+v\n", prepareResult)

	// 恢复备份（move-back）
	fmt.Println("Restoring backup...")
	restoreResult, err := manager.RestoreBackup(ctx, restoreOpts)
	if err != nil {
		fmt.Printf("Restore backup failed: %v\n", err)
		return
	}
	fmt.Printf("Restore backup completed: %+v\n", restoreResult)
}

// ExampleBackupControl 展示备份控制功能
func ExampleBackupControl() {
	config := &Config{
		BinPath:      "bin/percona-xtrabackup-2.4.15-Linux-x86_64/bin/innobackupex",
		MySQLBaseDir: "/home/<USER>/mysql",
		MySQLUser:    "backup_user",
		MySQLPassword: "backup_password",
		ThrottleRate: 50, // 初始限速 50 MB/s
	}

	manager := NewManager(config)

	// 启动备份（在另一个 goroutine 中）
	go func() {
		ctx := context.Background()
		opts := &BackupOptions{
			TargetDir:   "/backup/controlled",
			ExtraLSNDir: "/tmp/extra_lsn_control",
		}
		
		result, err := manager.FullBackup(ctx, opts)
		if err != nil {
			fmt.Printf("Controlled backup failed: %v\n", err)
		} else {
			fmt.Printf("Controlled backup completed: %+v\n", result)
		}
	}()

	// 等待备份开始
	time.Sleep(2 * time.Second)

	// 检查状态
	fmt.Printf("Current status: %v\n", manager.GetStatus())
	fmt.Printf("Is running: %v\n", manager.IsRunning())

	// 动态调整限速
	fmt.Println("Setting throttle rate to 100 MB/s...")
	if err := manager.SetThrottleRate(100); err != nil {
		fmt.Printf("Failed to set throttle rate: %v\n", err)
	}

	// 暂停备份
	time.Sleep(5 * time.Second)
	fmt.Println("Pausing backup...")
	if err := manager.Pause(); err != nil {
		fmt.Printf("Failed to pause backup: %v\n", err)
	}

	// 等待一段时间后恢复
	time.Sleep(3 * time.Second)
	fmt.Println("Resuming backup...")
	if err := manager.Resume(); err != nil {
		fmt.Printf("Failed to resume backup: %v\n", err)
	}

	// 等待备份完成或手动停止
	time.Sleep(10 * time.Second)
	if manager.IsRunning() {
		fmt.Println("Stopping backup...")
		if err := manager.Stop(); err != nil {
			fmt.Printf("Failed to stop backup: %v\n", err)
		}
	}
}

// ExampleProgressMonitoring 展示进度监控功能
func ExampleProgressMonitoring() {
	config := &Config{
		BinPath:      "bin/percona-xtrabackup-2.4.15-Linux-x86_64/bin/innobackupex",
		MySQLBaseDir: "/home/<USER>/mysql",
		MySQLUser:    "backup_user",
		MySQLPassword: "backup_password",
	}

	manager := NewManager(config)

	// 设置详细的进度回调
	manager.monitor.SetCallback(func(info *ProgressInfo) {
		switch info.Status {
		case StatusRunning:
			fmt.Printf("[RUNNING] Progress: %.2f%%, Speed: %d MB/s, ETA: %v\n",
				info.Progress, info.Speed, info.ETA)
			if info.CurrentFile != "" {
				fmt.Printf("  Current file: %s\n", info.CurrentFile)
			}
		case StatusPaused:
			fmt.Printf("[PAUSED] Backup paused at %.2f%%\n", info.Progress)
		case StatusCompleted:
			fmt.Printf("[COMPLETED] Backup finished successfully\n")
		case StatusFailed:
			fmt.Printf("[FAILED] Backup failed: %s\n", info.Message)
		}
	})

	// 启动备份
	ctx := context.Background()
	opts := &BackupOptions{
		TargetDir:   "/backup/monitored",
		ExtraLSNDir: "/tmp/extra_lsn_monitor",
	}

	// 在单独的 goroutine 中监控进度
	go func() {
		for {
			if !manager.IsRunning() {
				break
			}
			
			progress := manager.GetProgress()
			fmt.Printf("Status check - Progress: %.2f%%, Status: %v\n", 
				progress.Progress, progress.Status)
			
			time.Sleep(5 * time.Second)
		}
	}()

	// 执行备份
	result, err := manager.FullBackup(ctx, opts)
	if err != nil {
		fmt.Printf("Monitored backup failed: %v\n", err)
	} else {
		fmt.Printf("Monitored backup completed: %+v\n", result)
	}
}

// ExampleBackupValidation 展示备份验证功能
func ExampleBackupValidation() {
	config := &Config{
		BinPath: "bin/percona-xtrabackup-2.4.15-Linux-x86_64/bin/innobackupex",
	}

	manager := NewManager(config)

	// 验证备份
	backupPath := "/backup/full/20241203"
	fmt.Printf("Validating backup: %s\n", backupPath)
	
	if err := manager.ValidateBackup(backupPath); err != nil {
		fmt.Printf("Backup validation failed: %v\n", err)
		return
	}
	
	fmt.Println("Backup validation passed")

	// 获取备份信息
	info, err := manager.GetBackupInfo(backupPath)
	if err != nil {
		fmt.Printf("Failed to get backup info: %v\n", err)
		return
	}
	
	fmt.Printf("Backup info: %+v\n", info)
}

// ExampleDecompression 展示解压功能
func ExampleDecompression() {
	config := &Config{
		BinPath:      "bin/percona-xtrabackup-2.4.15-Linux-x86_64/bin/innobackupex",
		XbstreamPath: "bin/percona-xtrabackup-2.4.15-Linux-x86_64/bin/xbstream",
	}

	manager := NewManager(config)

	// 解压 xbstream 备份文件
	xbstreamFile := "/backup/compressed/backup.xbstream"
	fmt.Printf("Decompressing backup: %s\n", xbstreamFile)
	
	if err := manager.DecompressBackup(xbstreamFile, 16); err != nil {
		fmt.Printf("Decompression failed: %v\n", err)
		return
	}
	
	fmt.Println("Decompression completed successfully")
}
