package worker

import (
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"gopkg.in/ini.v1"

	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"

	"mdc-agent/common"
)

//检查主备状态
func HandleCheckBkStatus(taskMsg *pb_agent.MdcAgentSyncMsg) (*pb_agent.CheckBkMysqlStatusRes, error) {
	bkPath := taskMsg.GetCheckBkmysqlStatus().BkPath
	if bkPath == "" {
		errMsg := fmt.Sprintf("Invalid parameter, bkPath=[%v]", bkPath)
		common.Log.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	//获得io状态
	ioThreadsStr := fmt.Sprintf("%v/bin/mysql --defaults-extra-file=%v/etc/user.root.cnf -e \"show slave status\\G\"|grep %v|awk -F: '{print $2}'|sed 's/ //g'",
		bkPath, bkPath, common.IoThread)
	ioStatus, _, err := common.SyncExecShell(ioThreadsStr)
	if err != nil {
		common.Log.Error("SyncExecShell get slave status failed,err=[%v],shell=[%v]", err, ioThreadsStr)
		return nil, err
	}
	//获得sql状态
	sqlThreadsStr := fmt.Sprintf("%v/bin/mysql --defaults-extra-file=%v/etc/user.root.cnf -e \"show slave status\\G\"|grep %v|grep -v %v|awk -F: '{print $2}'|sed 's/ //g'",
		bkPath, bkPath, common.SqlThread, common.SlaveSQLRunningState)
	sqlStatus, _, err := common.SyncExecShell(sqlThreadsStr)
	if err != nil {
		common.Log.Error("SyncExecShell get slave status failed,err=[%v],shell=[%v]", err, sqlThreadsStr)
		return nil, err
	}
	//改为ini包读取获得备库账户
	//加载user配置文件
	//ShadowLoad解决相同相同key值只保留一个的问题
	cfg, err := ini.LoadSources(ini.LoadOptions{AllowBooleanKeys: true,
		IgnoreInlineComment: true, AllowShadows: true}, fmt.Sprintf("%v/etc/user.root.cnf", bkPath))
	if err != nil {
		errMsg := fmt.Sprintf("Load mysql user config failed, err=[%v]", err)
		common.Log.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	// 获取用户账号
	user := cfg.Section("client").Key("user").String()
	// 获取用户密码
	password := cfg.Section("client").Key("password").String()

	if user == "" || password == "" {
		errMsg := fmt.Sprintf("ini get slave user or password failed, err=[%v]", err)
		common.Log.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	if strings.Trim(ioStatus, " \n\r\t") == common.ThreadStatus && strings.Trim(sqlStatus, " \n\r\t") == common.ThreadStatus {
		respons := &pb_agent.CheckBkMysqlStatusRes{
			IoThread:     true,
			SqlThread:    true,
			XtraUser:     user,
			XtraPassword: password,
		}
		return respons, nil
	}
	errMsg := fmt.Sprintf("slave status id unnormal,io_thread=[%v],sql_thread=[%v]", ioStatus, sqlStatus)
	common.Log.Error(errMsg)
	return nil, errors.New(errMsg)
}

//处理获得磁盘余量任务
func HandleGetFreeCap(taskMsg *pb_agent.MdcAgentSyncMsg) (*pb_agent.GetFreeStorageRes, error) {
	filePath := taskMsg.GetGetFreeStorage().DataDir
	if filePath == "" {
		errMsg := fmt.Sprintf("Invalid parameter, filePath=[%v]", filePath)
		common.Log.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	diskStatus, err := common.DiskUsage(filePath)
	if err != nil {
		common.Log.Error("get disk usage failed,err=[%v]", err)
		return nil, err
	}
	systemStatusCheckRespons := &pb_agent.GetFreeStorageRes{
		FreeCapacity: int64(diskStatus.Free),
	}
	return systemStatusCheckRespons, nil
}

//处理清理数据任务
func HandleCleanData(taskMsg *pb_agent.MdcAgentSyncMsg) (err error) {
	dataType := taskMsg.GetCleanData().CleanDataType
	switch dataType {
	case pb_server.DataType_BK_REMOTE_DATA:
		err = common.CleanMachineData(taskMsg.GetCleanData().DataDir, dataType)
	case pb_server.DataType_BOS_DATA:
		err = common.DeleteBosData(taskMsg.GetCleanData().DataDir)
	case pb_server.DataType_RS_REMOTE_DATA:
		err = common.CleanMachineData(taskMsg.GetCleanData().DataDir, dataType)
	default:
		errMsg := fmt.Sprintf("Unknown dataType=[%v]", dataType)
		common.Log.Error(errMsg)
		return errors.New(errMsg)
	}
	return
}

//处理删除binlog备份
func HandleDeleteBinlogBk(taskMsg *pb_agent.MdcAgentSyncMsg) (err error) {
	//需要删除数据的目录
	bkBinlogDir := taskMsg.GetDeleteBinlogBk().BkBinlogDir
	binlogFileNameResult := taskMsg.GetDeleteBinlogBk().BinlogFileNameResult
	if bkBinlogDir == "" || binlogFileNameResult == "" {
		return nil
	}
	fileNameList := strings.Split(binlogFileNameResult, ",")
	if len(fileNameList) == 0 {
		return nil
	}
	for _, val := range fileNameList {
		fileName := fmt.Sprintf("%v/%v", bkBinlogDir, val)
		isExist, err := common.PathExists(fileName)
		if err != nil {
			errMsg := fmt.Sprintf("check file exists failed,err=[%v],fileName=[%v]", err, fileName)
			common.Log.Error(errMsg)
			return errors.New(errMsg)
		}
		if isExist {
			err = os.Remove(fileName)
			if err != nil {
				errMsg := fmt.Sprintf("remove file failed,err=[%v],fileName=[%v]", err, fileName)
				common.Log.Error(errMsg)
				return errors.New(errMsg)
			}
		}
	}

	return nil
}

//处理打包任务
func HandleUnpackData(taskMsg *pb_agent.MdcAgentAsyncMsg) (report *pb_server.UnpackDataReport, err error) {
	var (
		// Binlog的目标存储路径
		binlogTargetPath string
	)
	//需要一个目标目录
	dataSource := taskMsg.GetUnpackData().DataSource
	targetPath := taskMsg.GetUnpackData().TargetPath
	response := &pb_server.UnpackDataReport{
		BinlogTaskId: taskMsg.GetUnpackData().BinlogTaskId,
	}
	if targetPath == "" {
		errMsg := fmt.Sprintf("HandleUnpackData failed,err=[%v],dataSource=[%v],targetPath=[%v]", err, dataSource, targetPath)
		common.Log.Error(errMsg)
		return response, errors.New(errMsg)
	}
	filePathList := strings.Split(targetPath, "/")
	index := strings.LastIndex(targetPath, "/")
	if len(filePathList) < 2 {
		errMsg := fmt.Sprintf("HandleUnpackData failed,err=[%v],dataSource=[%v],targetPath=[%v]", err, dataSource, targetPath)
		common.Log.Error(errMsg)
		return response, errors.New(errMsg)
	}
	// 是否为新版分开打包逻辑
	isXbsteramType := strings.Contains(targetPath, common.XbstreamType)
	// 根据逻辑设置不同的binlog打包路径
	if isXbsteramType {
		binlogTargetPath = fmt.Sprintf("%v.%v", targetPath, common.BinlogPathName)
	} else {
		//拉取数据到目标机器对应的目录上
		binlogTargetPath = fmt.Sprintf("%v/%v", targetPath[0:index], common.BinlogPathName)
	}
	if dataSource != nil {
		//创建目录
		if err = os.MkdirAll(binlogTargetPath, os.ModePerm); err != nil {
			errMsg := fmt.Sprintf("HandleUnpackData MkdirAll failed,err=[%v],targetPath=[%v]", err, targetPath)
			common.Log.Error(errMsg)
			return response, errors.New(errMsg)
		}
		for _, val := range dataSource {
			if val == nil || val.DataDir == nil || len(val.DataDir) == 0 {
				continue
			}
			for _, data := range val.DataDir {
				fileNameList := strings.Split(data, "/")
				if len(fileNameList) < 2 {
					errMsg := fmt.Sprintf("HandleUnpackData ScpData failed,err=[%v],fileName=[%v],fileNameList=[%v]", err, val, fileNameList)
					common.Log.Error(errMsg)
					return response, errors.New(errMsg)
				}
				binlogTargetFileName := fmt.Sprintf("%v/%v", binlogTargetPath, fileNameList[len(fileNameList)-1])
				// 从远程大磁盘拷贝到打包机器的本地，并且删除远程磁盘的binlog
				err = common.ScpBinlogRemoteToLocalAndDelete(val.SourceIp, data, binlogTargetFileName)
				if err != nil {
					errMsg := fmt.Sprintf("HandleUnpackData ScpData binlogFailed. failed,err=[%v] dataSource=[%v] targetPath=[%v]", err, dataSource, targetPath)
					common.Log.Error(errMsg)
					continue
				}
			}
		}
	}
	if isXbsteramType {
		// 实际上就是binlog。
		if dataSource != nil {
			dir, file := filepath.Split(binlogTargetPath)
			tarBinlogName := fmt.Sprintf("%v.%v", file, common.UnpackType)
			// xbstream类型打包，只打包binlog
			response.PackBinlogPath = fmt.Sprintf("%v/%v", dir, tarBinlogName)
			tarShell := fmt.Sprintf("cd %v && tar -cf %v %v && rm -rf %v", dir, tarBinlogName, file, file)
			_, status, errTar := common.SyncExecShell(tarShell)
			if errTar != nil || status != 0 {
				errMsg := fmt.Sprintf("SyncExecShell tar binlog failed. err=[%v] targetPath=[%v] shell=[%v]", err, targetPath, tarShell)
				common.Log.Error(errMsg)
				return response, errors.New(errMsg)
			}
		}
	} else {
		// 仅执行打包操作不进行压缩,避免占用机器资源引发负载升高
		tarShell := fmt.Sprintf("cd %v && tar -cPf ./%v.%v ../%v", targetPath[0:index], filePathList[len(filePathList)-1], common.UnpackType, filePathList[len(filePathList)-2])
		_, status, errTar := common.SyncExecShell(tarShell)
		if errTar != nil || status != 0 {
			errMsg := fmt.Sprintf("SyncExecShell tar failed。 err=[%v] targetPath=[%v] shell=[%v]", err, targetPath, tarShell)
			common.Log.Error(errMsg)
			return response, errors.New(errMsg)
		}
	}

	return response, nil
}

//获得目录下文件的最后打开时间
// TODO: delete it 之前旧逻辑有问题.
func GetUploadBinlogFileList(bkBinlogPath, endUpdateTime string) (beginUpdatetime, newEndUpadetime string, binlogFileNameResult []string, binlogFullFileNameResult []string, err error) {
	fileNameMap := make(map[string]string)
	fileResult := []string{}
	//读取目录下的binlog文件
	fis, err := ioutil.ReadDir(bkBinlogPath)
	if err != nil {
		errMsg := fmt.Sprintf("ReadDir failed,err=[%v],pathname=[%v]", err, bkBinlogPath)
		common.Log.Error(errMsg)
		return "", "", nil, nil, errors.New(errMsg)
	}
	//判断当前拉取的binlog文件个数 若只有一个 则跳过本次上传
	if len(fis) == 1 {
		common.Log.Notice("GetUploadBinlogFileLis,fis=[%v]", fis)
		return "", "", nil, nil, nil
	}
	//构建完整的目录文件名并构建map
	for _, fi := range fis {
		fullname := bkBinlogPath + "/" + fi.Name()
		fileResult = append(fileResult, fullname)
		if fileNameMap == nil {
			fileNameMap = make(map[string]string)
		}
		fileNameMap[fullname] = fi.Name()
	}

	//判断文件列表中可以上传到远程磁盘的文件并获得对应的范围及最晚的更新时间
	// 初始化返回binlog文件时间范文初始值
	beginUpdatetime = time.Now().Format(common.TIME_FORMAT)
	newEndUpadetime = time.Now().Format(common.TIME_FORMAT)
	for i := 0; i < len(fileResult); i++ {
		// 获取文件最终修改时间
		finfo, _ := os.Stat(fileResult[i])
		fileEndTime := finfo.ModTime().Format(common.TIME_FORMAT)
		var fileName string
		common.Log.Warn("fileName=[%v],fileEndTime=[%v],endUpdateTime=[%v]", fileResult[i], fileEndTime, endUpdateTime)
		//线上集群有探活数据写入 因此文件的修改时间一直在变化 因此通过时间是否更新来判断文件是否可以上传
		if fileEndTime >= endUpdateTime && fileEndTime < time.Now().Format(common.TIME_FORMAT) {
			if _, ok := fileNameMap[fileResult[i]]; ok {
				fileName = fileNameMap[fileResult[i]]
			}
			fileNameTokens := strings.Split(fileName, "/")
			if len(fileNameTokens) <= 0 {
				errMsg := fmt.Sprintf("GetFiletime failed,err=[%v],pathname=[%v]", err, bkBinlogPath)
				common.Log.Error(errMsg)
				return "", "", nil, nil, errors.New(errMsg)
			}
			binlogFileNameResult = append(binlogFileNameResult, fileResult[i])
			binlogFullFileNameResult = append(binlogFullFileNameResult, fileNameTokens[len(fileNameTokens)-1])
			if beginUpdatetime > fileEndTime {
				beginUpdatetime = fileEndTime
			}
			if newEndUpadetime < fileEndTime {
				newEndUpadetime = fileEndTime
			}
			common.Log.Warn("add binlogFile fileName=[%v],beginUpdatetime=[%v],newEndUpadetime=[%v]", fileResult[i], beginUpdatetime, newEndUpadetime)
		}
	}
	return
}

// 获取可以上传的binlog文件逻辑
// TODO: 原来实现有问题，重写后当前只保证了数据可以不缺失, 不保证时间
func NewGetUploadBinlogFileList(bkBinlogPath string) (beginUpdatetime, newEndUpdatetime string, binlogFullFileNameResult []string, err error) {
	var (
		lastModify time.Time
	)
	// 读取目录下的binlog文件
	output, status, err := common.SyncExecShell(fmt.Sprintf("ls -rt %s|grep mysql-bin|grep -v 'tar.gz'", bkBinlogPath))
	if err != nil || status != 0 {
		errMsg := fmt.Sprintf("get upload binlog list failed. err=[%v] status=[%v] pathname=[%v] output=[%v]", err, status, bkBinlogPath, output)
		common.Log.Error(errMsg)
		return "", "", nil, errors.New(errMsg)
	}
	// 获取完整binlog列表
	binlogList := strings.Split(strings.TrimRight(output, "\n"), "\n")
	// 遍历binlog列表
	for idx, name := range binlogList {
		fullname := bkBinlogPath + "/" + name
		finfo, errMod := os.Stat(fullname)
		if errMod != nil {
			common.Log.Error("can't stat file=[%v]", name)
			continue
		}
		binlogFullFileNameResult = append(binlogFullFileNameResult, fullname)
		// 初始化第一个文件修改时间
		if idx == 0 {
			lastModify = finfo.ModTime()
		} else {
			// 第二个文件跟第一个文件修改时间若一样，则对文件名进行排序
			if lastModify == finfo.ModTime() {
				// 若后面的名称比前面的小
				// mysql-bin.003744 < mysql-bin.003745
				if binlogList[idx] < binlogList[idx-1] {
					// 交换顺序
					binlogFullFileNameResult[len(binlogFullFileNameResult)-2], binlogFullFileNameResult[len(binlogFullFileNameResult)-1] =
						binlogFullFileNameResult[len(binlogFullFileNameResult)-1], binlogFullFileNameResult[len(binlogFullFileNameResult)-2]
				}
			}
			lastModify = finfo.ModTime()
		}
	}

	if len(binlogFullFileNameResult) >= 1 {
		// 去除最后一个binlog文件.
		binlogFullFileNameResult = binlogFullFileNameResult[:len(binlogFullFileNameResult)-1]
		if len(binlogFullFileNameResult) < 1 {
			return
		}
		// 获取第一个文件最终修改时间
		finfo, errMod := os.Stat(binlogFullFileNameResult[0])
		if errMod != nil {
			return beginUpdatetime, newEndUpdatetime, binlogFullFileNameResult, fmt.Errorf("can't stat file. file=[%v]", binlogFullFileNameResult[0])
		}
		beginUpdatetime = finfo.ModTime().Format(common.TIME_FORMAT)
		// 获取文件最终修改时间
		finfo, errMod = os.Stat(binlogFullFileNameResult[len(binlogFullFileNameResult)-1])
		if errMod != nil {
			return beginUpdatetime, newEndUpdatetime, binlogFullFileNameResult, fmt.Errorf("can't stat file. file=[%v]", binlogFullFileNameResult[len(binlogFullFileNameResult)-1])
		}
		newEndUpdatetime = finfo.ModTime().Format(common.TIME_FORMAT)
	}
	return
}

func HandleScpBinlogToRemote(bkBinlogPath, remoteHost, remotePath string) (string, string, string, error) {
	// 获取可以拷贝的全量binlog list
	beginUpdateTime, newEndUpadetime, binlogFullFileNameResult, err := NewGetUploadBinlogFileList(bkBinlogPath)
	common.Log.Debug("NewGetUploadBinlogFileList binlogFullFileNameResult=[%v]", binlogFullFileNameResult)
	if err != nil {
		errMsg := fmt.Sprintf("HandleScpBinlogToRemote failed,err=[%v] bkBinlogPath=[%v] binlogFullFileNameResult=[%v]",
			err, bkBinlogPath, binlogFullFileNameResult)
		common.Log.Error(errMsg)
		return "", "", "", errors.New(errMsg)
	}
	var binlogList []string
	// 创建远端目录
	createRemotePath := fmt.Sprintf("ssh -i %v -o StrictHostKeyChecking=no mysql@%s \"[ -d %v ] || mkdir -p %v\"",
		common.DbbkRsaPath, remoteHost, remotePath, remotePath)
	_, _, err = common.SyncExecShell(createRemotePath)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell createRemotePath failed,err=[%v],shell=[%v]", err, createRemotePath)
		common.Log.Warn(errMsg)
		return "", "", "", errors.New(errMsg)
	}

	for _, fileFullPathFile := range binlogFullFileNameResult {
		// gzipBinlog
		gzipFile := gzipBinlog(fileFullPathFile)
		// 拷贝至大磁盘机器
		if err = common.ScpBinlogToRemoteAndDelete(remoteHost, remotePath, gzipFile); err != nil {
			errMsg := fmt.Sprintf("HandleScpBinlogToRemote failed,err=[%v]", err)
			common.Log.Warn(errMsg)
			continue
		}
		_, fileName := filepath.Split(gzipFile)
		binlogList = append(binlogList, fileName)
	}

	return beginUpdateTime, newEndUpadetime, strings.Join(binlogList, ","), nil
}

// 压缩binlog,并返回文件绝对路径
func gzipBinlog(fileFullPathFile string) string {
	path, fileName := filepath.Split(fileFullPathFile)
	if path != "" && fileName != "" {
		tarShell := fmt.Sprintf("cd %v && tar -czf %v.tar.gz %v && rm -f %v", path, fileName, fileName, fileName)
		output, status, err := common.SyncExecShell(tarShell)
		if status != 0 || err != nil {
			common.Log.Warn("fileFullPathFile=[%v] status=[%v] ouput=[%v] err=[%v]", fileFullPathFile, status, output, err)
			return fileFullPathFile
		}
		// 重写打包后带绝对路径文件
		return fmt.Sprintf("%v.tar.gz", fileFullPathFile)
	}
	return fileFullPathFile
}

//获得mysql实例的账户和密码
func getMasterMysqlInfo(bkPath string) (error, string, int, string, string) {
	//获得当前备库主库IP Port
	getIpShell := fmt.Sprintf("%v/bin/mysql --defaults-extra-file=%v/etc/user.root.cnf -e \"show slave status\\G\"|grep %v|awk -F: '{print $2}'|sed 's/ //g'",
		bkPath, bkPath, common.MasterHost)
	masterIp, _, err := common.SyncExecShell(getIpShell)
	if err != nil || masterIp == "" {
		errMsg := fmt.Sprintf("SyncExecShell get masterIp failed,err=[%v],shell=[%v]", err, getIpShell)
		common.Log.Warn(errMsg)
		return errors.New(errMsg), "", -1, "", ""
	}
	getPortShell := fmt.Sprintf("%v/bin/mysql --defaults-extra-file=%v/etc/user.root.cnf -e \"show slave status\\G\"|grep %v|awk -F: '{print $2}'|sed 's/ //g'",
		bkPath, bkPath, common.MasterPort)
	masterPortStr, _, err := common.SyncExecShell(getPortShell)
	if err != nil || masterPortStr == "" {
		errMsg := fmt.Sprintf("SyncExecShell get masterPortStr failed,err=[%v],shell=[%v]", err, getPortShell)
		common.Log.Warn(errMsg)
		return errors.New(errMsg), "", -1, "", ""
	}
	masterPort, err := strconv.Atoi(strings.Trim(masterPortStr, " \n\r\t"))
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell get master Port failed,err=[%v],masterPortStr=[%v]", err, masterPortStr)
		common.Log.Warn(errMsg)
		return errors.New(errMsg), "", -1, "", ""
	}
	//获得账户
	userShell := fmt.Sprintf("%v/bin/mysql --defaults-extra-file=%v/etc/user.root.cnf -e \"show slave status\\G\"|grep %v|awk -F: '{print $2}'|sed 's/ //g'",
		bkPath, bkPath, common.MasterUser)
	user, _, err := common.SyncExecShell(userShell)
	if err != nil || user == "" {
		errMsg := fmt.Sprintf("SyncExecShell get user failed,err=[%v],shell=[%v]", err, userShell)
		common.Log.Warn(errMsg)
		return errors.New(errMsg), "", -1, "", ""
	}
	//获得binlogFileName
	binlogFileShell := fmt.Sprintf("%v/bin/mysql --defaults-extra-file=%v/etc/user.root.cnf -e \"show slave status\\G\"|grep %v|awk -F: '{print $2}'|sed 's/ //g'",
		bkPath, bkPath, common.RelayMasterLogFile)
	binlogFile, _, err := common.SyncExecShell(binlogFileShell)
	if err != nil || binlogFile == "" {
		errMsg := fmt.Sprintf("SyncExecShell binlogFile failed,err=[%v],shell=[%v]", err, binlogFileShell)
		common.Log.Warn(errMsg)
		return errors.New(errMsg), "", -1, "", ""
	}
	return nil, strings.Trim(masterIp, " \n\r\t"), masterPort, strings.Trim(user, " \n\r\t"), strings.Trim(binlogFile, " \n\r\t")
}

//获得拉取数据目录下的binlog文件
func GetBinlogFileName(dataPath string, specifyPath string) ([]string, error) {
	if dataPath == "" || specifyPath == "" {
		errMsg := fmt.Sprintf("GetBinlogFileName failed,specifyPath=[%v],dataPath=[%v]", specifyPath, dataPath)
		common.Log.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	var dirList, targetFilePath []string
	err := filepath.Walk(dataPath,
		func(path string, f os.FileInfo, err error) error {
			if f == nil {
				return err
			}
			if f.IsDir() {
				dirList = append(dirList, path)
				return nil
			}
			return nil
		})
	if err != nil {
		errMsg := fmt.Sprintf("GetBinlogFileName failed,err=[%v],dataPath=[%v]", err, dataPath)
		common.Log.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	if len(dirList) != 0 {
		for _, value := range dirList {
			pathValueList := strings.Split(value, "/")
			if strings.Trim(pathValueList[len(pathValueList)-1], " \n\r\t") == specifyPath {
				//获得binlog文件所在的目录
				fileInfoList, err := ioutil.ReadDir(value)
				if err != nil {
					errMsg := fmt.Sprintf("GetBinlogFileName failed,err=[%v],dataPath=[%v]", err, dataPath)
					common.Log.Error(errMsg)
					return nil, errors.New(errMsg)
				}
				for _, file := range fileInfoList {
					targetFilePath = append(targetFilePath, fmt.Sprintf("%v/%v", value, file.Name()))
				}
			}
		}
	}
	return targetFilePath, nil
}
