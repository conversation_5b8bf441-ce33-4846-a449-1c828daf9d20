package utils

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"testing"
	"time"
)

// 测试执行命令
func TestExecCommand(t *testing.T) {
	// ExecCommand = fakeExecCommand

	output, err := ExecCommand(&Shell{
		Command: "netstat",
		Args:    []string{"-anlt | grep ESTABLISHED"},
		Timeout: 2 * time.Second,
	})
	if err != nil {
		t.<PERSON><PERSON>("Expected no error, got %v", err)
	}

	log.Println(output)
	if string(output) != "hello\n" {
		t.<PERSON><PERSON><PERSON>("Expected %q, got %q", "hello\n", string(output))
	}
}

func TestMockExecCommand(t *testing.T) {
	ActivateExecMock()
	ExpectExec(0, "hhhh")

	output, err := ExecCommand(&Shell{
		Command: "netstat",
		Args:    []string{"-anlt | grep ESTABLISHED"},
		Timeout: 2 * time.Second,
	})
	if err != nil {
		t.<PERSON><PERSON>("Expected no error, got %v", err)
	}

	if string(output) != "hhhh" {
		t.<PERSON>("Expected %q, got %q", "hhhh", string(output))
	}
}

func TestExecCommandHelper(t *testing.T) {
	if os.Getenv("GO_WANT_HELPER_PROCESS") != "1" {
		return
	}

	// println("Mocked stdout:", os.Getenv("STDOUT"))
	fmt.Fprint(os.Stdout, os.Getenv("STDOUT"))
	i, _ := strconv.Atoi(os.Getenv("EXIT_STATUS"))
	os.Exit(i)
}
