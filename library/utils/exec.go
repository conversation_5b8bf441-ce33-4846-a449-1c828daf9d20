package utils

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"
)

type Shell struct {
	Command string
	Args    []string
	Timeout time.Duration
	Retry   int
}

// 执行命令行程序
func execCommand(cfg *Shell) (string, error) {
	if cfg.Timeout == 0 {
		cfg.Timeout = 3 * time.Second
	}
	if cfg.Retry == 0 {
		cfg.Retry = 1
	}
	retry := cfg.Retry

	var output []byte
	var err error
	for i := 0; i < cfg.Retry; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), cfg.Timeout)
		output, err = exec.CommandContext(ctx, cfg.Command, cfg.Args...).CombinedOutput()
		cancel()
		if err != nil && i < retry-1 {
			continue
		}
		break
	}

	return string(output), err
}

var ExecCommand = execCommand

// ==========================================
//                  Mock
// ==========================================

var mockedExitStatus chan int
var mockedStdout chan string

func ActivateExecMock(cmdNum ...int) {
	num := 10
	if len(cmdNum) > 0 {
		num = cmdNum[0]
	}
	mockedExitStatus = make(chan int, num)
	mockedStdout = make(chan string, num)
	ExecCommand = fakeExecCommand
}

func DeactivateExecMock() {
	close(mockedExitStatus)
	close(mockedStdout)
	ExecCommand = execCommand
}

func ExpectExec(status int, stdout string) {
	mockedExitStatus <- status
	mockedStdout <- stdout
}

func fakeExecCommand(cfg *Shell) (string, error) {
	cs := []string{"-test.run=TestExecCommandHelper", "--", cfg.Command}
	cs = append(cs, cfg.Args...)
	cmd := exec.Command(os.Args[0], cs...)
	select {
	case status := <-mockedExitStatus:
		es := strconv.Itoa(status)
		cmd.Env = []string{"GO_WANT_HELPER_PROCESS=1", "STDOUT=" + (<-mockedStdout), "EXIT_STATUS=" + es}
	default:
		return "", fmt.Errorf("unexpected exec command %s", cfg.Command)
	}

	stdout, err := cmd.CombinedOutput()
	output := strings.ReplaceAll(string(stdout), "warning: GOCOVERDIR not set, no coverage data emitted\n", "")

	return output, err
}
