package worker

import (
	"fmt"
	"testing"

	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"
	. "github.com/smartystreets/goconvey/convey"
)

func TestHandlExecxtra(t *testing.T) {
	taskMsg := pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_START_XTRA,
		MsgType: &pb_agent.MdcAgentAsyncMsg_StartXtrabkExecute{
			StartXtrabkExecute: &pb_agent.StartXtrabkExecute{
				BkBasedir:  "/home/<USER>/genggangfeng/mysql",
				BkUser:     "test",
				BkPassword: "test123",
				BkLsnPos:   "0",
				RemoteIp:   "*************",
				RemotePath: "/home/<USER>/genggangfeng/mysqlbackup/base",
			},
		},
	}
	res, err := HandlExecxtra(&taskMsg)
	Convey("test HandlExecxtra", t, func() {
		So(err, ShouldBeNil)
		So(res, ShouldNotBeNil)
		fmt.Println(res)
	})
}

func TestHandleUpLoadDbaBos(t *testing.T) {
	taskMsg := pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_UPLOADDBABOS,
		MsgType: &pb_agent.MdcAgentAsyncMsg_UploadDbaBos{
			UploadDbaBos: &pb_agent.UploadDbaBosData{
				DataDir:   "",
				DbaBosDir: "",
			},
		},
	}
	res, err := HandleUpLoadDbaBos(&taskMsg)
	Convey("test HandleUpLoadDbaBos", t, func() {
		So(err, ShouldBeNil)
		So(res, ShouldNotBeNil)
	})
}

func TestHandleStopXtraTask(t *testing.T) {
	taskMsg := pb_agent.MdcAgentSyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_STOP_XTRA,
		MsgType: &pb_agent.MdcAgentSyncMsg_StopXtrabkExecute{
			StopXtrabkExecute: &pb_agent.StopXtrabkExecute{},
		},
	}
	err := HandleStopXtraTask(&taskMsg)
	Convey("test HandleStopXtraTask", t, func() {
		So(err, ShouldBeNil)
	})
}

func TestHandleStartBinlogTask(t *testing.T) {
	taskMsg := pb_agent.MdcAgentSyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_EXEC_START_BINLOG,
		MsgType: &pb_agent.MdcAgentSyncMsg_ExecStartBinlog{
			ExecStartBinlog: &pb_agent.ExecStartBinlog{
				BkBinlogDir: "/home/<USER>/genggangfeng/mysqlbackup",
				BkMysqlPath: "/home/<USER>/genggangfeng/mysql",
			},
		},
	}

	res, err := HandleStartBinlogTask(&taskMsg)
	Convey("test HandleStartBinlogTask", t, func() {
		So(err, ShouldBeNil)
		So(res, ShouldNotBeNil)
		fmt.Println(res)
	})
}


func TestHandleCheckBinlogStatus(t *testing.T) {
	taskMsg := pb_agent.MdcAgentSyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_CHECK_BINLOGSTATUS,
		MsgType: &pb_agent.MdcAgentSyncMsg_CheckBinlogProcessStatus{
			CheckBinlogProcessStatus: &pb_agent.CheckBinlogProcessStatus{
				ServerId:       21966,
				BkBinlogDir:    "/home/<USER>/genggangfeng/mysqlbackup",
				BkMysqlPath:    "/home/<USER>/genggangfeng/mysql",
			},
		},
	}
	res, err := HandleCheckBinlogStatus(&taskMsg)
	Convey("test HandleCheckBinlogStatus", t, func() {
		So(err, ShouldBeNil)
		So(res, ShouldNotBeNil)
		fmt.Println(res)
	})
}

func TestHandleStopBinlogTask(t *testing.T) {
	taskMsg := pb_agent.MdcAgentSyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_EXEC_STOP_BINLOG,
		MsgType: &pb_agent.MdcAgentSyncMsg_ExecStopBinlog{
			ExecStopBinlog: &pb_agent.ExecStopBinlog{
				ServerId: 1,
			},
		},
	}
	err := HandleStopBinlogTask(&taskMsg)
	Convey("test HandleCheckBinlogStatus", t, func() {
		So(err, ShouldBeNil)
	})
}